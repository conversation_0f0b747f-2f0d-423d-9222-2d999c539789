#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配额管理脚本
用于创建充值卡、查看用户配额等管理操作
"""

import asyncio
import sys
import os
import uuid
from datetime import datetime, timedelta, timezone

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import AsyncSessionLocal, init_database
from app.models.quota_models import UserQuota, RechargeCard, QuotaUsageLog
from sqlalchemy import select

async def create_recharge_card(quota_hours: int, description: str = None) -> str:
    """创建充值卡"""
    card_id = str(uuid.uuid4())
    quota_seconds = quota_hours * 3600  # 转换为秒
    
    async with AsyncSessionLocal() as session:
        card = RechargeCard(
            card_id=card_id,
            quota_amount=quota_seconds,
            description=description or f"{quota_hours}小时套餐"
        )
        session.add(card)
        await session.commit()
    
    print(f"充值卡创建成功:")
    print(f"卡密: {card_id}")
    print(f"配额: {quota_hours}小时 ({quota_seconds}秒)")
    print(f"描述: {description or f'{quota_hours}小时套餐'}")
    return card_id

async def list_user_quota(user_id: str = None):
    """查看用户配额"""
    async with AsyncSessionLocal() as session:
        if user_id:
            result = await session.execute(
                select(UserQuota).where(UserQuota.user_id == user_id)
            )
            quotas = [result.scalar_one_or_none()]
        else:
            result = await session.execute(select(UserQuota))
            quotas = result.scalars().all()
        
        if not quotas or (len(quotas) == 1 and quotas[0] is None):
            print("未找到用户配额记录")
            return
        
        print("\n用户配额信息:")
        print("-" * 80)
        print(f"{'用户ID':<20} {'剩余配额(小时)':<15} {'总配额(小时)':<15} {'到期时间':<20}")
        print("-" * 80)
        
        for quota in quotas:
            if quota:
                balance_hours = quota.quota_balance / 3600
                total_hours = quota.total_quota / 3600
                expiry = quota.expiry_date.strftime("%Y-%m-%d %H:%M") if quota.expiry_date else "无限期"
                print(f"{quota.user_id:<20} {balance_hours:<15.2f} {total_hours:<15.2f} {expiry:<20}")

async def list_recharge_cards(show_used: bool = False):
    """查看充值卡"""
    async with AsyncSessionLocal() as session:
        if show_used:
            result = await session.execute(select(RechargeCard))
        else:
            result = await session.execute(
                select(RechargeCard).where(RechargeCard.is_used == False)
            )
        cards = result.scalars().all()
        
        if not cards:
            print("未找到充值卡记录")
            return
        
        print(f"\n充值卡信息 ({'包含已使用' if show_used else '仅未使用'}):")
        print("-" * 100)
        print(f"{'卡密':<40} {'配额(小时)':<12} {'状态':<8} {'使用者':<20} {'使用时间':<20}")
        print("-" * 100)
        
        for card in cards:
            quota_hours = card.quota_amount / 3600
            status = "已使用" if card.is_used else "未使用"
            used_by = card.used_by or "-"
            used_at = card.used_at.strftime("%Y-%m-%d %H:%M") if card.used_at else "-"
            print(f"{card.card_id:<40} {quota_hours:<12.1f} {status:<8} {used_by:<20} {used_at:<20}")

async def show_usage_logs(user_id: str = None, limit: int = 20):
    """查看配额使用日志"""
    async with AsyncSessionLocal() as session:
        query = select(QuotaUsageLog).order_by(QuotaUsageLog.created_at.desc()).limit(limit)
        if user_id:
            query = query.where(QuotaUsageLog.user_id == user_id)
        
        result = await session.execute(query)
        logs = result.scalars().all()
        
        if not logs:
            print("未找到使用日志")
            return
        
        print(f"\n配额使用日志 (最近{limit}条):")
        print("-" * 120)
        print(f"{'时间':<20} {'用户ID':<20} {'操作':<8} {'配额(秒)':<10} {'余额前':<10} {'余额后':<10} {'任务ID':<36}")
        print("-" * 120)
        
        for log in logs:
            created_at = log.created_at.strftime("%Y-%m-%d %H:%M:%S")
            print(f"{created_at:<20} {log.user_id:<20} {log.operation_type:<8} {log.quota_amount:<10} {log.balance_before:<10} {log.balance_after:<10} {log.task_id:<36}")

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("配额管理脚本")
        print("用法:")
        print("  python manage_quota.py create_card <小时数> [描述]  # 创建充值卡")
        print("  python manage_quota.py list_users [用户ID]        # 查看用户配额")
        print("  python manage_quota.py list_cards [--used]        # 查看充值卡")
        print("  python manage_quota.py usage_logs [用户ID] [数量] # 查看使用日志")
        print("  python manage_quota.py init_db                   # 初始化数据库")
        return
    
    command = sys.argv[1]
    
    try:
        if command == "init_db":
            await init_database()
            print("数据库初始化成功")
        
        elif command == "create_card":
            if len(sys.argv) < 3:
                print("错误: 请指定配额小时数")
                return
            hours = int(sys.argv[2])
            description = sys.argv[3] if len(sys.argv) > 3 else None
            await create_recharge_card(hours, description)
        
        elif command == "list_users":
            user_id = sys.argv[2] if len(sys.argv) > 2 else None
            await list_user_quota(user_id)
        
        elif command == "list_cards":
            show_used = "--used" in sys.argv
            await list_recharge_cards(show_used)
        
        elif command == "usage_logs":
            user_id = sys.argv[2] if len(sys.argv) > 2 else None
            limit = int(sys.argv[3]) if len(sys.argv) > 3 else 20
            await show_usage_logs(user_id, limit)
        
        else:
            print(f"未知命令: {command}")
    
    except Exception as e:
        print(f"执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
