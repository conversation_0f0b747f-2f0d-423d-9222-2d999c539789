# -*- coding: utf-8 -*-
"""
转录任务处理服务（带配额管理）
"""

import os
import logging
from app.services.task_service import TaskService
from app.services.srt_service import SrtService
from app.services.quota_service import QuotaService
from app.models.response_models import FileInfo
from app.models.request_models import TranscribeRequest
from app.services.elevenlabs_client import AsyncElevenLabsSTTClient

logger = logging.getLogger(__name__)

async def process_transcription_task_with_quota(
    task_id: str,
    file_path: str,
    request_params: TranscribeRequest,
    task_service: TaskService,
    user_id: str,
    quota_service: QuotaService,
    file_info: FileInfo
):
    """处理转录任务（带配额管理）"""
    
    try:
        # 现有转录逻辑保持不变
        await task_service.update_task_progress(task_id, 10, "开始调用 ElevenLabs API...")

        async with AsyncElevenLabsSTTClient() as client:
            api_response = await client.transcribe_file(file_path, request_params)

        await task_service.update_task_progress(task_id, 80, "处理转录结果...")

        srt_service = SrtService()
        processed_result = await srt_service.process_additional_formats(task_id, api_response)

        await task_service.update_task_progress(task_id, 95, "生成字幕文件...")

        processing_time = await task_service.calculate_processing_time(task_id)

        # 任务完成后扣除实际配额
        if file_info.duration:
            actual_duration = int(file_info.duration)  # 转换为整数秒
            deduct_success = await quota_service.deduct_quota_after_completion(
                user_id, task_id, actual_duration
            )
            if not deduct_success:
                logger.warning(f"任务{task_id}完成但配额扣除失败")

        await task_service.complete_task(task_id, processed_result, processing_time)

    except Exception as e:
        await task_service.fail_task(task_id, str(e))

    finally:
        # 清理临时文件
        try:
            os.remove(file_path)
        except:
            pass
