# -*- coding: utf-8 -*-
"""
管理员API - 用户管理
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from app.auth.admin_dependencies import verify_admin_jwt
from app.core.database import get_db_session
from app.services.admin_service import AdminService
from app.models.admin_models import (
    UserListRequest, ModifyUserQuotaRequest, UsageLogListRequest,
    UserListResponse, AdminResponse, UserStatus
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/users", tags=["管理员-用户管理"])

@router.get("", response_model=UserListResponse, summary="查看用户列表")
async def list_users(
    limit: int = Query(50, ge=1, le=200, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    search: Optional[str] = Query(None, max_length=100, description="搜索用户ID"),
    status: Optional[UserStatus] = Query(None, description="用户状态"),
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    查看用户列表
    
    - **limit**: 每页数量，最大200
    - **offset**: 偏移量
    - **search**: 搜索用户ID
    - **status**: 用户状态 (active/exhausted)
    """
    try:
        request = UserListRequest(
            limit=limit,
            offset=offset,
            search=search,
            status=status
        )
        
        admin_service = AdminService(db_session)
        result = await admin_service.list_users(request)
        
        logger.info(f"管理员 {admin_user} 查看用户列表: 状态={status}, 数量={len(result['users'])}")
        
        return UserListResponse(
            success=True,
            data=result
        )
        
    except Exception as e:
        logger.error(f"查看用户列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "查询失败",
                "message": "查看用户列表时发生错误",
                "hint": "请稍后重试或联系管理员"
            }
        )

@router.get("/{user_id}", response_model=AdminResponse, summary="查看用户详情")
async def get_user_detail(
    user_id: str = Path(..., description="用户ID"),
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    查看用户详情
    
    - **user_id**: 用户ID
    """
    try:
        # 查询单个用户详情
        request = UserListRequest(limit=1, offset=0, search=user_id)
        admin_service = AdminService(db_session)
        result = await admin_service.list_users(request)
        
        if not result['users']:
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "用户不存在",
                    "message": f"用户 {user_id} 不存在",
                    "hint": "请检查用户ID是否正确"
                }
            )
        
        user_info = result['users'][0]
        logger.info(f"管理员 {admin_user} 查看用户详情: {user_id}")
        
        return AdminResponse(
            success=True,
            data=user_info,
            message="查询成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查看用户详情失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "查询失败",
                "message": "查看用户详情时发生错误"
            }
        )

@router.patch("/{user_id}/quota", response_model=AdminResponse, summary="修改用户配额")
async def modify_user_quota(
    user_id: str = Path(..., description="用户ID"),
    request: ModifyUserQuotaRequest = ...,
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    修改用户配额
    
    - **user_id**: 用户ID
    - **operation**: 操作类型 (add/set/subtract)
    - **quota_hours**: 配额小时数
    - **reason**: 操作原因
    """
    try:
        admin_service = AdminService(db_session)
        result = await admin_service.modify_user_quota(user_id, request)
        
        logger.info(f"管理员 {admin_user} 修改用户配额: {user_id}, 操作: {request.operation}, "
                   f"变化: {request.quota_hours}小时, 原因: {request.reason}")
        
        return AdminResponse(
            success=True,
            data=result,
            message=f"用户 {user_id} 配额修改成功"
        )
        
    except Exception as e:
        logger.error(f"修改用户配额失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "修改失败",
                "message": "修改用户配额时发生错误",
                "hint": "请检查参数或稍后重试"
            }
        )

@router.get("/{user_id}/logs", response_model=AdminResponse, summary="查看用户使用日志")
async def get_user_logs(
    user_id: str = Path(..., description="用户ID"),
    limit: int = Query(100, ge=1, le=1000, description="数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    查看用户使用日志
    
    - **user_id**: 用户ID
    - **limit**: 数量限制，最大1000
    - **offset**: 偏移量
    """
    try:
        request = UsageLogListRequest(
            user_id=user_id,
            limit=limit,
            offset=offset
        )
        
        admin_service = AdminService(db_session)
        result = await admin_service.list_usage_logs(request)
        
        logger.info(f"管理员 {admin_user} 查看用户使用日志: {user_id}, 数量={len(result['logs'])}")
        
        return AdminResponse(
            success=True,
            data=result,
            message="查询成功"
        )
        
    except Exception as e:
        logger.error(f"查看用户使用日志失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "查询失败",
                "message": "查看用户使用日志时发生错误"
            }
        )
