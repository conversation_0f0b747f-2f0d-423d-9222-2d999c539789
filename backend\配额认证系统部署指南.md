# 配额认证系统部署指南

## 🎯 系统概述

独立配额认证系统已成功集成到现有后端，提供以下功能：

- **JWT代理认证**：通过Worker验证JWT token
- **配额管理**：按音频时长计费的配额系统
- **卡密充值**：支持充值卡充值配额
- **事后扣费**：业务完成后才扣除实际配额
- **友好错误**：详细的错误信息和用户指导

## 🚀 部署步骤

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

新增的依赖包括：
- `sqlalchemy[asyncio]==2.0.23` - 异步数据库ORM
- `aiosqlite==0.19.0` - SQLite异步驱动
- `alembic==1.13.1` - 数据库迁移工具

### 2. 配置环境变量

复制并编辑环境变量文件：

```bash
cp .env.example .env
```

关键配置项：

```env
# 配额认证系统配置
WORKER_BASE_URL=https://your-worker.domain.com
WORKER_VERIFY_ENDPOINT=/api/auth/verify
DATABASE_URL=sqlite+aiosqlite:///./quota.db
ENABLE_QUOTA_SYSTEM=true

# 管理员API配置
ENABLE_ADMIN_API=true
ADMIN_USERS=555,admin_user,super_admin,manager_001
```

### 3. 初始化数据库

```bash
python scripts/manage_quota.py init_db
```

### 4. 启动服务

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🛠️ 管理操作

### 管理脚本帮助

```bash
# 查看所有可用命令
python scripts/manage_quota.py

# 输出:
# 配额管理脚本
# 用法:
#   python manage_quota.py create_card <小时数> [描述]  # 创建充值卡
#   python manage_quota.py list_users [用户ID]        # 查看用户配额
#   python manage_quota.py list_cards [--used]        # 查看充值卡
#   python manage_quota.py usage_logs [用户ID] [数量] # 查看使用日志
#   python manage_quota.py init_db                   # 初始化数据库
```

### 创建充值卡

```bash
# 创建10小时套餐充值卡
python scripts/manage_quota.py create_card 10 "月套餐-10小时"

# 创建100小时套餐充值卡
python scripts/manage_quota.py create_card 100 "年套餐-100小时"
```

**输出示例**:
```
充值卡创建成功:
卡密: 12345678-1234-1234-1234-123456789abc
配额: 10小时 (36000秒)
描述: 月套餐-10小时
```

### 查看用户配额

```bash
# 查看所有用户配额
python scripts/manage_quota.py list_users

# 查看特定用户配额
python scripts/manage_quota.py list_users user123
```

**输出示例**:
```
用户配额信息:
--------------------------------------------------------------------------------
用户ID               剩余配额(小时)    总配额(小时)     到期时间
--------------------------------------------------------------------------------
555                  8.50            10.00           无限期
user123              0.00            5.00            2024-12-31 23:59
```

### 查看充值卡

```bash
# 查看未使用的充值卡
python scripts/manage_quota.py list_cards

# 查看所有充值卡（包含已使用）
python scripts/manage_quota.py list_cards --used
```

**输出示例**:
```
充值卡信息 (仅未使用):
----------------------------------------------------------------------------------------------------
卡密                                     配额(小时)   状态     使用者               使用时间
----------------------------------------------------------------------------------------------------
12345678-1234-1234-1234-123456789abc     10.0        未使用   -                    -
87654321-4321-4321-4321-cba987654321     5.0         已使用   555                  2024-01-15 14:30
```

### 查看使用日志

```bash
# 查看最近20条使用日志
python scripts/manage_quota.py usage_logs

# 查看特定用户的使用日志
python scripts/manage_quota.py usage_logs user123 50
```

### 批量操作

#### 批量创建充值卡

```bash
# 创建多张充值卡的脚本示例
for i in {1..10}; do
  python scripts/manage_quota.py create_card 10 "批量套餐-$i"
done
```

#### 导出充值卡信息

```bash
# 导出未使用的充值卡到文件
python scripts/manage_quota.py list_cards > unused_cards.txt

# 导出所有充值卡到文件
python scripts/manage_quota.py list_cards --used > all_cards.txt
```

## 🔧 API接口

### 1. JWT认证上传接口

**端点**: `POST /api/v1/transcribe/upload-jwt`

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**请求体**: 与原有上传接口相同

**响应状态码**:
- `200`: 上传成功，任务已创建
- `401`: JWT认证失败（token无效或过期）
- `402`: 配额不足（需要充值）
- `413`: 文件过大（超过1GB限制）
- `422`: 参数错误（文件格式不支持等）

### 2. 获取配额信息

**端点**: `GET /api/v1/quota/info`

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**响应示例**:
```json
{
  "user_id": "user123",
  "quota_balance": 36000,
  "total_quota": 36000,
  "expiry_date": null,
  "status": "active"
}
```

### 3. 卡密充值

**端点**: `POST /api/v1/quota/recharge`

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**请求体**:
```json
{
  "card_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
}
```

**成功响应示例**:
```json
{
  "success": true,
  "message": "充值成功，获得36000秒配额"
}
```

**失败响应示例**:
```json
{
  "detail": {
    "error": "充值失败",
    "message": "充值卡不存在或已被使用"
  }
}
```

### 4. 管理员API（新增）

**端点**: `GET /api/v1/admin/stats/overview`

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**功能说明**:
- 管理员API基于JWT认证，复用现有Worker验证
- 通过管理员用户列表控制访问权限
- 提供完整的卡密管理、用户管理和统计功能

**主要端点**:
- 卡密管理: `/api/v1/admin/cards`
- 用户管理: `/api/v1/admin/users`
- 统计信息: `/api/v1/admin/stats`
- 日志查询: `/api/v1/admin/logs`

**使用示例**:
```bash
# 查看系统统计
curl -X GET http://localhost:8000/api/v1/admin/stats/overview \
  -H "Authorization: Bearer <admin_jwt_token>"

# 创建充值卡
curl -X POST http://localhost:8000/api/v1/admin/cards \
  -H "Authorization: Bearer <admin_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"quota_hours": 10, "description": "管理员创建"}'
```

详细说明请参考：[管理员API接口文档.md](./管理员API接口文档.md)

## 🔄 系统兼容性

### 向后兼容

- **原有API保持不变**：`/api/v1/transcribe/upload` 继续使用API密钥认证
- **新增JWT API**：`/api/v1/transcribe/upload-jwt` 使用JWT+配额认证
- **配额系统开关**：通过 `ENABLE_QUOTA_SYSTEM` 控制是否启用

### 渐进式部署

1. **第一阶段**：部署代码，`ENABLE_QUOTA_SYSTEM=false`
2. **第二阶段**：配置Worker验证端点
3. **第三阶段**：启用配额系统，`ENABLE_QUOTA_SYSTEM=true`
4. **第四阶段**：前端切换到JWT认证接口

## 🧪 测试验证

### 运行测试脚本

```bash
python test_quota_system.py
```

测试内容包括：
- API端点可用性
- JWT认证流程
- 配额检查机制
- 充值功能

### 手动测试

1. **创建充值卡**：
```bash
python scripts/manage_quota.py create_card 1 "测试1小时"
```

2. **测试充值**：
```bash
curl -X POST http://localhost:8000/api/v1/quota/recharge \
  -H "Authorization: Bearer test_token" \
  -H "Content-Type: application/json" \
  -d '{"card_id": "your_card_id"}'
```

## 📊 监控指标

### 数据库表结构

- **user_quotas**: 用户配额信息
- **quota_usage_logs**: 配额使用记录
- **recharge_cards**: 充值卡信息

### 关键监控点

1. **配额余额监控**：用户配额不足告警
2. **使用量统计**：每日/每月配额消耗统计
3. **充值监控**：充值卡使用情况
4. **认证失败率**：JWT认证失败统计

## 🔒 安全考虑

### 已解决的安全问题

1. ✅ **密钥硬编码**：移除了硬编码的API密钥
2. ✅ **代理认证**：JWT验证委托给Worker
3. ✅ **配额控制**：防止无限制使用
4. ✅ **事务安全**：配额扣除的原子性

### 建议的安全措施

1. **HTTPS强制**：生产环境必须使用HTTPS
2. **JWT过期**：设置合理的JWT过期时间
3. **速率限制**：添加API调用频率限制
4. **审计日志**：记录所有配额操作

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 配置
   - 确保数据库文件权限正确

2. **JWT验证失败**
   - 检查 `WORKER_BASE_URL` 配置
   - 确保Worker验证端点可访问

3. **配额扣除失败**
   - 检查数据库事务日志
   - 查看 `quota_usage_logs` 表

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看配额相关日志
grep "配额" logs/app.log
```

## 📈 性能优化

### 数据库优化

1. **索引优化**：为常用查询字段添加索引
2. **连接池**：配置合适的数据库连接池
3. **定期清理**：清理过期的使用日志

### 缓存策略

1. **配额缓存**：缓存用户配额信息
2. **JWT缓存**：缓存JWT验证结果
3. **Redis集成**：利用现有Redis进行缓存

## 🎉 总结

配额认证系统已成功集成，具备以下优势：

- ✅ **完全兼容**：不影响现有功能
- ✅ **安全可靠**：解决了密钥安全问题
- ✅ **易于管理**：提供完整的管理工具
- ✅ **可扩展性**：支持未来功能扩展
- ✅ **商业化就绪**：支持配额计费模式

系统现在可以安全地提供给前端使用，支持基于JWT的认证和按时长计费的配额管理。
