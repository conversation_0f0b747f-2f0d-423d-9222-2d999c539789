# ElevenLabs STT 后端服务开发实施手册

## 📋 项目概述

本手册详细说明如何将 `scribe2srt` 项目中的 ElevenLabs Speech-to-Text API 客户端提取改造为独立的 Ubuntu 服务器后端项目，支持前端自定义参数配置。

## 🎯 技术架构

### 架构图
```
前端 (React/Vue) → Nginx → FastAPI 后端 → Redis → ElevenLabs API
                                    ↓
                              Celery Worker
```

### 技术栈
- **后端框架**: FastAPI + uvicorn
- **异步处理**: asyncio + aiohttp
- **任务队列**: Redis + 后台任务
- **文件处理**: aiofiles + multipart
- **部署**: Docker + Docker Compose

## 🔧 API 参数配置

### 可自定义参数列表

| 参数名 | 类型 | 是否必需 | 默认值 | 说明 | 前端控制 |
|--------|------|----------|--------|------|----------|
| `model_id` | string | ✅ | `scribe_v1_experimental` | 转录模型ID | ❌ 固定值 |
| `file` | file | ✅ | - | 音频/视频文件 | ✅ 文件上传 |
| `language_code` | string/null | ❌ | `null` | 语言代码 | ✅ 下拉选择 |
| `tag_audio_events` | boolean | ❌ | `true` | 标记音频事件 | ✅ 开关控制 |
| `diarize` | boolean | ❌ | `false` | 说话人识别 | ✅ 开关控制 |
| `num_speakers` | integer/null | ❌ | `null` | 最大说话人数量 | ✅ 数字输入 |
| `timestamps_granularity` | enum | ❌ | `word` | 时间戳粒度 | ✅ 单选按钮 |
| `additional_formats` | object | ❌ | `null` | 额外导出格式 | ✅ 表单配置 |

### 参数详细说明

#### 1. model_id (固定参数)
```json
{
  "model_id": "scribe_v1_experimental"  // 或 "scribe_v1"
}
```
**前端实现**: 隐藏字段，后端固定设置

#### 2. language_code (可选参数)
```json
{
  "language_code": "zh"  // ISO-639-1 或 ISO-639-3 代码
}
```
**前端实现**: 下拉选择框
```javascript
const languageOptions = [
  { value: null, label: "自动检测" },
  { value: "zh", label: "中文" },
  { value: "en", label: "英语" },
  { value: "ja", label: "日语" },
  { value: "ko", label: "韩语" },
  { value: "es", label: "西班牙语" },
  { value: "fr", label: "法语" },
  { value: "de", label: "德语" },
  { value: "ru", label: "俄语" }
];
```

#### 3. tag_audio_events (可选参数)
```json
{
  "tag_audio_events": true
}
```
**前端实现**: 开关控制
```javascript
<Switch 
  checked={tagAudioEvents} 
  onChange={setTagAudioEvents}
  label="标记音频事件 (如笑声、脚步声等)"
/>
```

#### 4. diarize (可选参数)
```json
{
  "diarize": false  // 是否启用说话人识别
}
```
**前端实现**: 开关控制
```javascript
<Switch
  checked={diarize}
  onChange={setDiarize}
  label="说话人识别 (识别音频中的不同说话人)"
/>
```

#### 5. num_speakers (可选参数)
```json
{
  "num_speakers": 2  // 1-32 之间的整数
}
```
**前端实现**: 数字输入框
```javascript
<NumberInput
  min={1}
  max={32}
  value={numSpeakers}
  onChange={setNumSpeakers}
  placeholder="自动检测"
  label="最大说话人数量"
/>
```

#### 6. timestamps_granularity (可选参数)
```json
{
  "timestamps_granularity": "word"  // "none" | "word" | "character"
}
```
**前端实现**: 单选按钮组
```javascript
const granularityOptions = [
  { value: "none", label: "无时间戳" },
  { value: "word", label: "单词级时间戳" },
  { value: "character", label: "字符级时间戳" }
];
```

#### 6. additional_formats (可选参数)
```json
{
  "additional_formats": [
    {
      "format": "srt",
      "max_characters_per_line": 50,
      "include_speakers": false,
      "include_timestamps": true,
      "segment_on_silence_longer_than_s": 0.6,
      "max_segment_duration_s": 5,
      "max_segment_chars": 90
    }
  ]
}
```
**前端实现**: 动态表单配置

## 🚀 后端实现

### 项目结构
```
elevenlabs-stt-backend/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI 应用入口
│   ├── models/
│   │   ├── __init__.py
│   │   ├── request_models.py   # 请求模型定义
│   │   └── response_models.py  # 响应模型定义
│   ├── services/
│   │   ├── __init__.py
│   │   ├── elevenlabs_client.py # ElevenLabs API 客户端
│   │   ├── file_service.py     # 文件处理服务
│   │   └── task_service.py     # 任务管理服务
│   ├── api/
│   │   ├── __init__.py
│   │   └── transcribe.py       # 转录API路由
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py           # 配置管理
│   │   └── dependencies.py     # 依赖注入
│   └── utils/
│       ├── __init__.py
│       ├── validators.py       # 参数验证
│       └── helpers.py          # 工具函数
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
└── README.md
```

### 核心代码实现

#### 1. 请求模型定义 (app/models/request_models.py)
```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Literal
from enum import Enum

class TimestampsGranularity(str, Enum):
    NONE = "none"
    WORD = "word"
    CHARACTER = "character"

class SrtExportOptions(BaseModel):
    format: Literal["srt"] = "srt"
    max_characters_per_line: Optional[int] = Field(default=50, ge=1, le=200)
    include_speakers: bool = Field(default=False)
    include_timestamps: bool = Field(default=True)
    segment_on_silence_longer_than_s: Optional[float] = Field(default=0.6, ge=0.1, le=10.0)
    max_segment_duration_s: Optional[float] = Field(default=5.0, ge=1.0, le=30.0)
    max_segment_chars: Optional[int] = Field(default=90, ge=10, le=500)

class TranscribeRequest(BaseModel):
    language_code: Optional[str] = Field(default=None, max_length=10)
    tag_audio_events: bool = Field(default=True)
    num_speakers: Optional[int] = Field(default=None, ge=1, le=32)
    timestamps_granularity: TimestampsGranularity = Field(default=TimestampsGranularity.WORD)
    additional_formats: Optional[List[SrtExportOptions]] = Field(default=None)
    
    @validator('language_code')
    def validate_language_code(cls, v):
        if v is not None:
            # 验证语言代码格式
            valid_codes = ['zh', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'ru', 'it', 'pt']
            if v not in valid_codes:
                raise ValueError(f'不支持的语言代码: {v}')
        return v

class ChunkUploadRequest(BaseModel):
    task_id: str = Field(..., min_length=1)
    chunk_index: int = Field(..., ge=0)
    total_chunks: int = Field(..., ge=1)
```

#### 2. 响应模型定义 (app/models/response_models.py)
```python
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class FileInfo(BaseModel):
    filename: str
    size: int
    duration: Optional[float] = None
    mime_type: str

class TranscribeResponse(BaseModel):
    task_id: str
    status: TaskStatus
    message: str
    file_info: Optional[FileInfo] = None

class TaskStatusResponse(BaseModel):
    task_id: str
    status: TaskStatus
    progress: int = Field(..., ge=0, le=100)
    message: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    processing_time: Optional[float] = None

class TranscriptionResult(BaseModel):
    language_code: str
    language_probability: float
    text: str
    words: List[Dict[str, Any]]
    additional_formats: Optional[List[Dict[str, Any]]] = None
```

#### 3. ElevenLabs API 客户端 (app/services/elevenlabs_client.py)
```python
import aiohttp
import aiofiles
import os
import random
import json
from typing import Optional, Dict, Any, Callable
from app.models.request_models import TranscribeRequest, SrtExportOptions

class AsyncElevenLabsSTTClient:
    """异步 ElevenLabs STT 客户端"""

    API_URL = "https://api.elevenlabs.io/v1/speech-to-text"
    API_PARAMS = {"allow_unauthenticated": "1"}
    MODEL_ID = "scribe_v1_experimental"  # 固定使用 scribe_v1_experimental

    # 保持原有的请求头配置
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15",
    ]

    ACCEPT_LANGUAGES = [
        "zh-CN,zh;q=0.9,en;q=0.8",
        "en-US,en;q=0.9,es;q=0.8",
        "ja-JP,ja;q=0.9,en;q=0.8",
        "ko-KR,ko;q=0.9,en;q=0.8",
    ]

    BASE_HEADERS = {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "origin": "https://elevenlabs.io",
        "referer": "https://elevenlabs.io/",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
    }

    def __init__(self, session: Optional[aiohttp.ClientSession] = None):
        self.session = session or aiohttp.ClientSession()
        self._own_session = session is None

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._own_session and self.session:
            await self.session.close()

    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        headers = self.BASE_HEADERS.copy()
        headers["user-agent"] = random.choice(self.USER_AGENTS)
        headers["accept-language"] = random.choice(self.ACCEPT_LANGUAGES)
        return headers

    def _detect_mime_type(self, file_path: str) -> str:
        """检测 MIME 类型"""
        ext = os.path.splitext(file_path)[1].lower()
        mime_map = {
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.flac': 'audio/flac',
            '.m4a': 'audio/m4a',
            '.aac': 'audio/aac',
            '.ogg': 'audio/ogg',
            '.mp4': 'video/mp4',
            '.mov': 'video/mov',
        }
        return mime_map.get(ext, 'application/octet-stream')

    async def transcribe_file(
        self,
        file_path: str,
        request_params: TranscribeRequest,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> Dict[str, Any]:
        """转录音频文件"""

        headers = self._build_headers()
        mime_type = self._detect_mime_type(file_path)

        # 构建表单数据
        data = aiohttp.FormData()
        data.add_field('model_id', self.MODEL_ID)
        data.add_field('diarize', 'true')  # 固定启用说话人识别
        data.add_field('tag_audio_events', str(request_params.tag_audio_events).lower())
        data.add_field('timestamps_granularity', request_params.timestamps_granularity.value)

        # 可选参数
        if request_params.language_code:
            data.add_field('language_code', request_params.language_code)

        if request_params.num_speakers:
            data.add_field('num_speakers', str(request_params.num_speakers))

        if request_params.additional_formats:
            formats_json = json.dumps([fmt.dict() for fmt in request_params.additional_formats])
            data.add_field('additional_formats', formats_json)

        # 添加文件
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()
            data.add_field(
                'file',
                file_content,
                filename=os.path.basename(file_path),
                content_type=mime_type
            )

        # 发送请求
        timeout = aiohttp.ClientTimeout(total=1800)  # 30分钟超时

        async with self.session.post(
            self.API_URL,
            params=self.API_PARAMS,
            headers=headers,
            data=data,
            timeout=timeout
        ) as response:
            response.raise_for_status()
            return await response.json()
```

#### 4. 主要 API 路由 (app/api/transcribe.py)
```python
from fastapi import APIRouter, File, UploadFile, Form, BackgroundTasks, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Optional
import uuid
import os
import json
from datetime import datetime

from app.models.request_models import TranscribeRequest, SrtExportOptions
from app.models.response_models import TranscribeResponse, TaskStatusResponse, FileInfo
from app.services.elevenlabs_client import AsyncElevenLabsSTTClient
from app.services.task_service import TaskService
from app.services.file_service import FileService
from app.utils.validators import validate_file_format

router = APIRouter(prefix="/api/v1/transcribe", tags=["转录"])

@router.post("/upload", response_model=TranscribeResponse)
async def upload_audio(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="音频或视频文件"),
    language_code: Optional[str] = Form(None, description="语言代码 (如: zh, en, ja)"),
    tag_audio_events: bool = Form(True, description="是否标记音频事件"),
    num_speakers: Optional[int] = Form(None, ge=1, le=32, description="最大说话人数量"),
    timestamps_granularity: str = Form("word", description="时间戳粒度: none/word/character"),
    additional_formats: Optional[str] = Form(None, description="额外格式配置 (JSON字符串)"),
    task_service: TaskService = Depends(),
    file_service: FileService = Depends()
):
    """
    上传音频文件进行转录

    支持的文件格式: MP3, WAV, FLAC, M4A, AAC, OGG, MP4, MOV
    最大文件大小: 1GB
    """

    # 生成任务ID
    task_id = str(uuid.uuid4())

    try:
        # 验证文件格式
        validate_file_format(file.filename)

        # 解析额外格式配置
        parsed_additional_formats = None
        if additional_formats:
            try:
                formats_data = json.loads(additional_formats)
                parsed_additional_formats = [SrtExportOptions(**fmt) for fmt in formats_data]
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"额外格式配置解析错误: {e}")

        # 构建请求参数
        request_params = TranscribeRequest(
            language_code=language_code,
            tag_audio_events=tag_audio_events,
            num_speakers=num_speakers,
            timestamps_granularity=timestamps_granularity,
            additional_formats=parsed_additional_formats
        )

        # 保存上传文件
        file_path = await file_service.save_upload_file(task_id, file)

        # 获取文件信息
        file_info = await file_service.get_file_info(file_path)

        # 初始化任务状态
        await task_service.create_task(task_id, file_info, request_params)

        # 启动后台转录任务
        background_tasks.add_task(
            process_transcription_task,
            task_id=task_id,
            file_path=file_path,
            request_params=request_params,
            task_service=task_service
        )

        return TranscribeResponse(
            task_id=task_id,
            status="processing",
            message="文件上传成功，开始处理",
            file_info=file_info
        )

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/status/{task_id}", response_model=TaskStatusResponse)
async def get_transcription_status(
    task_id: str,
    task_service: TaskService = Depends()
):
    """查询转录任务状态"""

    task_status = await task_service.get_task_status(task_id)

    if not task_status:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

    return task_status

async def process_transcription_task(
    task_id: str,
    file_path: str,
    request_params: TranscribeRequest,
    task_service: TaskService
):
    """处理转录任务"""

    try:
        # 更新任务状态
        await task_service.update_task_progress(task_id, 10, "开始调用 ElevenLabs API...")

        # 执行转录
        async with AsyncElevenLabsSTTClient() as client:
            result = await client.transcribe_file(file_path, request_params)

        # 计算处理时间
        processing_time = await task_service.calculate_processing_time(task_id)

        # 更新完成状态
        await task_service.complete_task(task_id, result, processing_time)

    except Exception as e:
        # 更新错误状态
        await task_service.fail_task(task_id, str(e))

    finally:
        # 清理临时文件
        try:
            os.remove(file_path)
        except:
            pass
```

## 🎨 前端实现示例

### React 组件实现

#### 1. 主要上传组件 (TranscribeUpload.jsx)
```jsx
import React, { useState, useCallback } from 'react';
import { Upload, Button, Form, Select, Switch, InputNumber, Card, Progress } from 'antd';
import { InboxOutlined } from '@ant-design/icons';

const { Dragger } = Upload;
const { Option } = Select;

const TranscribeUpload = () => {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [taskId, setTaskId] = useState(null);

  // 语言选项
  const languageOptions = [
    { value: null, label: '自动检测' },
    { value: 'zh', label: '中文' },
    { value: 'en', label: '英语' },
    { value: 'ja', label: '日语' },
    { value: 'ko', label: '韩语' },
    { value: 'es', label: '西班牙语' },
    { value: 'fr', label: '法语' },
    { value: 'de', label: '德语' },
    { value: 'ru', label: '俄语' }
  ];

  // 时间戳粒度选项
  const granularityOptions = [
    { value: 'none', label: '无时间戳' },
    { value: 'word', label: '单词级时间戳' },
    { value: 'character', label: '字符级时间戳' }
  ];

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.mp3,.wav,.flac,.m4a,.aac,.ogg,.mp4,.mov',
    beforeUpload: (file) => {
      // 验证文件大小 (1GB)
      const isLt1GB = file.size / 1024 / 1024 / 1024 < 1;
      if (!isLt1GB) {
        message.error('文件大小不能超过 1GB!');
        return false;
      }
      return false; // 阻止自动上传
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  // 提交表单
  const handleSubmit = async (values) => {
    const { file, additionalFormats, ...otherValues } = values;

    if (!file || file.length === 0) {
      message.error('请选择要上传的文件');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file[0].originFileObj);

      // 添加其他参数
      Object.keys(otherValues).forEach(key => {
        if (otherValues[key] !== undefined && otherValues[key] !== null) {
          formData.append(key, otherValues[key]);
        }
      });

      // 处理额外格式配置
      if (additionalFormats && additionalFormats.enabled) {
        const formats = [{
          format: 'srt',
          max_characters_per_line: additionalFormats.maxCharactersPerLine || 50,
          include_speakers: additionalFormats.includeSpeakers || false,
          include_timestamps: additionalFormats.includeTimestamps !== false,
          segment_on_silence_longer_than_s: additionalFormats.segmentOnSilence || 0.6,
          max_segment_duration_s: additionalFormats.maxSegmentDuration || 5,
          max_segment_chars: additionalFormats.maxSegmentChars || 90
        }];
        formData.append('additional_formats', JSON.stringify(formats));
      }

      // 发送请求
      const response = await fetch('/api/v1/transcribe/upload', {
        method: 'POST',
        body: formData,
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(progress);
        }
      });

      const result = await response.json();

      if (response.ok) {
        setTaskId(result.task_id);
        message.success('文件上传成功，开始处理');
        // 开始轮询任务状态
        pollTaskStatus(result.task_id);
      } else {
        throw new Error(result.detail || '上传失败');
      }

    } catch (error) {
      message.error(`上传失败: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  // 轮询任务状态
  const pollTaskStatus = useCallback(async (taskId) => {
    try {
      const response = await fetch(`/api/v1/transcribe/status/${taskId}`);
      const status = await response.json();

      setUploadProgress(status.progress);

      if (status.status === 'completed') {
        message.success('转录完成!');
        // 处理结果
        console.log('转录结果:', status.result);
      } else if (status.status === 'failed') {
        message.error(`转录失败: ${status.error}`);
      } else {
        // 继续轮询
        setTimeout(() => pollTaskStatus(taskId), 2000);
      }
    } catch (error) {
      message.error(`状态查询失败: ${error.message}`);
    }
  }, []);

  return (
    <Card title="音频转录" style={{ maxWidth: 800, margin: '0 auto' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          tag_audio_events: true,
          diarize: false,
          timestamps_granularity: 'word',
          additionalFormats: {
            enabled: false,
            maxCharactersPerLine: 50,
            includeSpeakers: false,
            includeTimestamps: true,
            segmentOnSilence: 0.6,
            maxSegmentDuration: 5,
            maxSegmentChars: 90
          }
        }}
      >
        {/* 文件上传 */}
        <Form.Item
          name="file"
          label="选择音频/视频文件"
          rules={[{ required: true, message: '请选择要转录的文件' }]}
        >
          <Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 MP3, WAV, FLAC, M4A, AAC, OGG, MP4, MOV 格式，最大 1GB
            </p>
          </Dragger>
        </Form.Item>

        {/* 语言选择 */}
        <Form.Item
          name="language_code"
          label="语言"
          tooltip="指定音频语言可以提高转录准确性"
        >
          <Select placeholder="选择语言或自动检测">
            {languageOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* 音频事件标记 */}
        <Form.Item
          name="tag_audio_events"
          label="音频事件标记"
          valuePropName="checked"
          tooltip="是否在转录中标记笑声、脚步声等音频事件"
        >
          <Switch />
        </Form.Item>

        {/* 说话人识别 */}
        <Form.Item
          name="diarize"
          label="说话人识别"
          valuePropName="checked"
          tooltip="是否启用说话人识别功能，识别音频中的不同说话人"
        >
          <Switch />
        </Form.Item>

        {/* 说话人数量 */}
        <Form.Item
          name="num_speakers"
          label="最大说话人数量"
          tooltip="指定音频中的最大说话人数量，有助于提高说话人识别准确性"
        >
          <InputNumber
            min={1}
            max={32}
            placeholder="自动检测"
            style={{ width: '100%' }}
          />
        </Form.Item>

        {/* 时间戳粒度 */}
        <Form.Item
          name="timestamps_granularity"
          label="时间戳粒度"
          tooltip="选择时间戳的详细程度"
        >
          <Select>
            {granularityOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* 额外格式配置 */}
        <Card title="SRT 字幕格式配置" size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name={['additionalFormats', 'enabled']}
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.additionalFormats?.enabled !== currentValues.additionalFormats?.enabled
            }
          >
            {({ getFieldValue }) => {
              const enabled = getFieldValue(['additionalFormats', 'enabled']);
              return enabled ? (
                <>
                  <Form.Item
                    name={['additionalFormats', 'maxCharactersPerLine']}
                    label="每行最大字符数"
                  >
                    <InputNumber min={10} max={200} />
                  </Form.Item>

                  <Form.Item
                    name={['additionalFormats', 'includeSpeakers']}
                    label="包含说话人信息"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    name={['additionalFormats', 'includeTimestamps']}
                    label="包含时间戳"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    name={['additionalFormats', 'segmentOnSilence']}
                    label="静音分段阈值 (秒)"
                  >
                    <InputNumber min={0.1} max={10} step={0.1} />
                  </Form.Item>

                  <Form.Item
                    name={['additionalFormats', 'maxSegmentDuration']}
                    label="最大段落时长 (秒)"
                  >
                    <InputNumber min={1} max={30} />
                  </Form.Item>

                  <Form.Item
                    name={['additionalFormats', 'maxSegmentChars']}
                    label="最大段落字符数"
                  >
                    <InputNumber min={10} max={500} />
                  </Form.Item>
                </>
              ) : null;
            }}
          </Form.Item>
        </Card>

        {/* 进度条 */}
        {(uploading || taskId) && (
          <Form.Item>
            <Progress
              percent={uploadProgress}
              status={uploading ? "active" : "normal"}
              showInfo={true}
            />
          </Form.Item>
        )}

        {/* 提交按钮 */}
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={uploading}
            size="large"
            block
          >
            {uploading ? '处理中...' : '开始转录'}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TranscribeUpload;
```

#### 2. 任务状态组件 (TaskStatus.jsx)
```jsx
import React, { useState, useEffect } from 'react';
import { Card, Progress, Typography, Button, Space, Alert } from 'antd';
import { DownloadOutlined, EyeOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

const TaskStatus = ({ taskId }) => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (taskId) {
      pollStatus();
    }
  }, [taskId]);

  const pollStatus = async () => {
    try {
      const response = await fetch(`/api/v1/transcribe/status/${taskId}`);
      const data = await response.json();
      setStatus(data);

      // 如果任务还在进行中，继续轮询
      if (data.status === 'processing') {
        setTimeout(pollStatus, 2000);
      }
    } catch (error) {
      console.error('获取状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const downloadResult = () => {
    if (status?.result) {
      const blob = new Blob([JSON.stringify(status.result, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transcription_${taskId}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  if (loading) {
    return <Card loading />;
  }

  if (!status) {
    return <Alert message="任务不存在" type="error" />;
  }

  return (
    <Card title={`任务状态: ${taskId}`}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Progress
          percent={status.progress}
          status={status.status === 'failed' ? 'exception' : 'normal'}
          showInfo={true}
        />

        <Text strong>状态: {status.status}</Text>
        <Text>消息: {status.message}</Text>

        {status.file_info && (
          <div>
            <Text strong>文件信息:</Text>
            <ul>
              <li>文件名: {status.file_info.filename}</li>
              <li>大小: {(status.file_info.size / 1024 / 1024).toFixed(2)} MB</li>
              {status.file_info.duration && (
                <li>时长: {Math.floor(status.file_info.duration / 60)}分{Math.floor(status.file_info.duration % 60)}秒</li>
              )}
            </ul>
          </div>
        )}

        {status.status === 'completed' && status.result && (
          <div>
            <Title level={4}>转录结果</Title>
            <Paragraph>
              <Text strong>检测语言:</Text> {status.result.language_code}
              (置信度: {(status.result.language_probability * 100).toFixed(1)}%)
            </Paragraph>

            <Paragraph>
              <Text strong>转录文本:</Text>
            </Paragraph>
            <Paragraph
              style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                maxHeight: '200px',
                overflow: 'auto'
              }}
            >
              {status.result.text}
            </Paragraph>

            <Space>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={downloadResult}
              >
                下载完整结果
              </Button>

              {status.result.additional_formats && (
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    const srtContent = status.result.additional_formats[0]?.content;
                    if (srtContent) {
                      const blob = new Blob([srtContent], { type: 'text/plain' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `subtitles_${taskId}.srt`;
                      a.click();
                      URL.revokeObjectURL(url);
                    }
                  }}
                >
                  下载 SRT 字幕
                </Button>
              )}
            </Space>
          </div>
        )}

        {status.status === 'failed' && (
          <Alert
            message="转录失败"
            description={status.error}
            type="error"
            showIcon
          />
        )}

        {status.processing_time && (
          <Text type="secondary">
            处理时间: {status.processing_time.toFixed(1)} 秒
          </Text>
        )}
      </Space>
    </Card>
  );
};

export default TaskStatus;
```

## 🚀 部署配置

### 1. Docker 配置

#### Dockerfile
```dockerfile
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ ./app/

# 创建必要的目录
RUN mkdir -p /tmp/uploads /tmp/chunks

# 设置环境变量
ENV PYTHONPATH=/app
ENV REDIS_URL=redis://redis:6379/0

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # Redis 服务
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # 后端 API 服务
  api:
    build: .
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - MAX_UPLOAD_SIZE=**********  # 1GB
      - MAX_CONCURRENT_TASKS=3
      - MAX_REQUESTS_PER_MINUTE=30
    volumes:
      - ./uploads:/tmp/uploads
      - ./logs:/app/logs
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api

volumes:
  redis_data:
```

#### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    upstream api_backend {
        server api:8000;
    }

    # 文件上传大小限制
    client_max_body_size 1G;

    # 超时设置
    proxy_connect_timeout 60s;
    proxy_send_timeout 1800s;
    proxy_read_timeout 1800s;

    server {
        listen 80;
        server_name localhost;

        # API 代理
        location /api/ {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 静态文件服务 (前端)
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

        # 健康检查
        location /health {
            proxy_pass http://api_backend/health;
        }
    }
}
```

### 2. 环境配置

#### requirements.txt
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
aiohttp==3.9.1
aiofiles==23.2.1
redis==5.0.1
python-multipart==0.0.6
pydantic==2.5.0
uvloop==0.19.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
```

#### .env 配置文件
```env
# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
MAX_UPLOAD_SIZE=**********  # 1GB
UPLOAD_DIR=/tmp/uploads
CHUNK_DIR=/tmp/chunks

# 并发控制
MAX_CONCURRENT_TASKS=3
MAX_REQUESTS_PER_MINUTE=30

# ElevenLabs API 配置
ELEVENLABS_API_URL=https://api.elevenlabs.io/v1/speech-to-text
ELEVENLABS_TIMEOUT=1800

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# 安全配置
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]
```

## 📝 使用说明

### 1. 快速启动

```bash
# 克隆项目
git clone <repository-url>
cd elevenlabs-stt-backend

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f api
```

### 2. API 测试

```bash
# 健康检查
curl http://localhost:8000/health

# 上传文件测试
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -F "file=@test.mp3" \
  -F "language_code=zh" \
  -F "tag_audio_events=true" \
  -F "num_speakers=2"

# 查询状态
curl http://localhost:8000/api/v1/transcribe/status/{task_id}
```

### 3. 前端集成

```javascript
// 安装依赖
npm install axios antd

// 使用组件
import TranscribeUpload from './components/TranscribeUpload';

function App() {
  return (
    <div className="App">
      <TranscribeUpload />
    </div>
  );
}
```

## 🔧 自定义配置

### 1. 修改并发限制

```python
# app/core/config.py
MAX_CONCURRENT_TASKS = 5  # 增加并发任务数
MAX_REQUESTS_PER_MINUTE = 50  # 增加速率限制
```

### 2. 添加新的语言支持

```python
# app/utils/validators.py
SUPPORTED_LANGUAGES = [
    'zh', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'ru', 'it', 'pt',
    'ar', 'hi', 'th', 'vi'  # 添加新语言
]
```

### 3. 自定义文件格式支持

```python
# app/utils/validators.py
ALLOWED_EXTENSIONS = {
    '.mp3', '.wav', '.flac', '.m4a', '.aac', '.ogg',
    '.mp4', '.mov', '.avi', '.mkv'  # 添加新格式
}
```

## 📊 监控和日志

### 1. 日志配置

```python
# app/core/logging.py
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            RotatingFileHandler('logs/app.log', maxBytes=10485760, backupCount=5),
            logging.StreamHandler()
        ]
    )
```

### 2. 性能监控

```python
# app/middleware/monitoring.py
import time
from fastapi import Request

async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

## 🚨 故障排除

### 常见问题

1. **文件上传失败**
   - 检查文件大小限制
   - 验证文件格式支持
   - 确认 Nginx 配置正确

2. **转录任务超时**
   - 增加超时时间设置
   - 检查 ElevenLabs API 状态
   - 验证网络连接

3. **Redis 连接失败**
   - 检查 Redis 服务状态
   - 验证连接配置
   - 查看防火墙设置

### 调试命令

```bash
# 查看容器状态
docker-compose ps

# 查看 API 日志
docker-compose logs api

# 进入容器调试
docker-compose exec api bash

# 测试 Redis 连接
docker-compose exec redis redis-cli ping
```

---

## 📚 总结

本实施手册提供了完整的 ElevenLabs STT 后端服务开发方案，包括：

- ✅ **完整的参数自定义支持**
- ✅ **现代化的 FastAPI 后端架构**
- ✅ **React 前端组件示例**
- ✅ **Docker 容器化部署**
- ✅ **生产环境配置**
- ✅ **监控和故障排除**

通过这个方案，可以快速构建一个功能完整、可扩展的语音转文字服务。
```
