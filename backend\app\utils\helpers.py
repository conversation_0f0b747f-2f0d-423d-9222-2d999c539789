import os
import uuid
import hashlib
from typing import Optional, Dict, Any
from datetime import datetime, timezone

def generate_task_id() -> str:
    """生成唯一的任务ID"""
    return str(uuid.uuid4())

def generate_file_hash(file_path: str) -> str:
    """生成文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def format_duration(seconds: Optional[float]) -> str:
    """格式化时长显示"""
    if seconds is None:
        return "未知"
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes:02d}:{secs:02d}"

def get_current_timestamp() -> str:
    """获取当前UTC时间戳"""
    return datetime.now(timezone.utc).isoformat()

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全字符"""
    # 移除路径分隔符和其他不安全字符
    unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # 限制文件名长度
    name, ext = os.path.splitext(filename)
    if len(name) > 100:
        name = name[:100]
    
    return name + ext

def extract_error_message(error: Exception) -> str:
    """提取错误信息"""
    error_msg = str(error)
    
    # 如果是HTTP错误，尝试提取更友好的错误信息
    if hasattr(error, 'response') and hasattr(error.response, 'json'):
        try:
            error_data = error.response.json()
            if 'detail' in error_data:
                return error_data['detail']
            elif 'message' in error_data:
                return error_data['message']
        except:
            pass
    
    return error_msg if error_msg else "未知错误"

def validate_json_string(json_str: str) -> Dict[str, Any]:
    """验证并解析JSON字符串"""
    import json
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        raise ValueError(f"无效的JSON格式: {e}")

def create_response_dict(
    success: bool = True,
    message: str = "",
    data: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None
) -> Dict[str, Any]:
    """创建标准响应字典"""
    response = {
        "success": success,
        "message": message,
        "timestamp": get_current_timestamp()
    }
    
    if data is not None:
        response["data"] = data
    
    if error is not None:
        response["error"] = error
    
    return response
