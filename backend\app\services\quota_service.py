# -*- coding: utf-8 -*-
"""
配额管理服务
"""

import aiohttp
import logging
from typing import Optional, <PERSON>ple
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.core.config import settings
from app.models.quota_models import UserQuota, QuotaUsageLog, RechargeCard

logger = logging.getLogger(__name__)

class QuotaService:
    """配额管理服务"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.worker_verify_url = f"{settings.WORKER_BASE_URL}{settings.WORKER_VERIFY_ENDPOINT}"
    
    async def verify_jwt_and_get_user(self, jwt_token: str) -> Optional[str]:
        """代理认证：向Worker验证JWT并获取用户名"""
        try:
            # 设置请求头 - Worker期望token在请求头中
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'ElevenLabs-STT-Backend/1.0.0',
                'Authorization': f'Bearer {jwt_token}'
            }

            # 空请求体
            request_data = {}

            # SSL配置
            ssl_context = False  # 禁用SSL验证
            connector = aiohttp.TCPConnector(ssl=ssl_context)

            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.post(
                    self.worker_verify_url,
                    json=request_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        try:
                            # 手动解析JSON，避免mimetype问题
                            import json
                            response_text = await response.text()
                            data = json.loads(response_text)

                            if data.get("success"):
                                return data.get("username")
                        except Exception:
                            pass

            return None

        except Exception as e:
            logger.error(f"JWT验证失败: {e}")
            return None
    
    async def check_quota_availability(self, user_id: str, required_duration: int) -> Tuple[bool, int, str]:
        """
        检查配额是否充足
        
        Returns:
            Tuple[bool, int, str]: (是否充足, 当前余额, 消息)
        """
        try:
            # 查询用户配额
            result = await self.db.execute(
                select(UserQuota).where(UserQuota.user_id == user_id)
            )
            user_quota = result.scalar_one_or_none()
            
            if not user_quota:
                # 用户不存在，创建默认配额记录
                user_quota = UserQuota(
                    user_id=user_id,
                    quota_balance=0,
                    total_quota=0
                )
                self.db.add(user_quota)
                await self.db.commit()
                return False, 0, "用户配额不足，请充值"
            
            # 检查是否过期
            if user_quota.expiry_date and user_quota.expiry_date < datetime.now(timezone.utc):
                return False, user_quota.quota_balance, "配额已过期，请重新充值"
            
            # 检查配额是否充足
            if user_quota.quota_balance >= required_duration:
                return True, user_quota.quota_balance, "配额充足"
            else:
                return False, user_quota.quota_balance, f"配额不足，需要{required_duration}秒，当前余额{user_quota.quota_balance}秒"
                
        except Exception as e:
            logger.error(f"检查配额失败: {e}")
            return False, 0, "配额检查失败，请稍后重试"
    
    async def deduct_quota_after_completion(
        self, 
        user_id: str, 
        task_id: str, 
        actual_duration: int
    ) -> bool:
        """业务完成后扣除实际配额"""
        try:
            # 获取当前配额
            result = await self.db.execute(
                select(UserQuota).where(UserQuota.user_id == user_id)
            )
            user_quota = result.scalar_one_or_none()
            
            if not user_quota or user_quota.quota_balance < actual_duration:
                logger.error(f"扣除配额失败，用户{user_id}配额不足")
                return False
            
            balance_before = user_quota.quota_balance
            balance_after = balance_before - actual_duration
            
            # 更新配额
            await self.db.execute(
                update(UserQuota)
                .where(UserQuota.user_id == user_id)
                .values(
                    quota_balance=balance_after,
                    last_updated=datetime.now(timezone.utc)
                )
            )
            
            # 记录使用日志
            usage_log = QuotaUsageLog(
                user_id=user_id,
                task_id=task_id,
                operation_type="deduct",
                quota_amount=actual_duration,
                audio_duration=actual_duration,
                balance_before=balance_before,
                balance_after=balance_after
            )
            self.db.add(usage_log)
            
            await self.db.commit()
            logger.info(f"用户{user_id}配额扣除成功，扣除{actual_duration}秒，余额{balance_after}秒")
            return True
            
        except Exception as e:
            logger.error(f"扣除配额失败: {e}")
            await self.db.rollback()
            return False

    async def get_user_quota_info(self, user_id: str) -> dict:
        """获取用户配额信息"""
        try:
            result = await self.db.execute(
                select(UserQuota).where(UserQuota.user_id == user_id)
            )
            user_quota = result.scalar_one_or_none()

            if not user_quota:
                return {
                    "user_id": user_id,
                    "quota_balance": 0,
                    "total_quota": 0,
                    "expiry_date": None,
                    "status": "no_quota"
                }

            return {
                "user_id": user_id,
                "quota_balance": user_quota.quota_balance,
                "total_quota": user_quota.total_quota,
                "expiry_date": user_quota.expiry_date.isoformat() if user_quota.expiry_date else None,
                "status": "active" if user_quota.quota_balance > 0 else "exhausted"
            }

        except Exception as e:
            logger.error(f"获取用户配额信息失败: {e}")
            return {"error": "获取配额信息失败"}

    async def recharge_with_card(self, user_id: str, card_id: str) -> Tuple[bool, str]:
        """使用卡密充值"""
        try:
            # 查询卡密
            result = await self.db.execute(
                select(RechargeCard).where(RechargeCard.card_id == card_id)
            )
            card = result.scalar_one_or_none()

            if not card:
                return False, "卡密不存在"

            if card.is_used:
                return False, "卡密已被使用"

            # 获取或创建用户配额记录
            result = await self.db.execute(
                select(UserQuota).where(UserQuota.user_id == user_id)
            )
            user_quota = result.scalar_one_or_none()

            if not user_quota:
                user_quota = UserQuota(
                    user_id=user_id,
                    quota_balance=card.quota_amount,
                    total_quota=card.quota_amount
                )
                self.db.add(user_quota)
            else:
                user_quota.quota_balance += card.quota_amount
                user_quota.total_quota += card.quota_amount
                user_quota.last_updated = datetime.now(timezone.utc)

            # 标记卡密为已使用
            card.is_used = True
            card.used_by = user_id
            card.used_at = datetime.now(timezone.utc)

            await self.db.commit()

            return True, f"充值成功，获得{card.quota_amount}秒配额"

        except Exception as e:
            logger.error(f"卡密充值失败: {e}")
            await self.db.rollback()
            return False, "充值失败，请稍后重试"
