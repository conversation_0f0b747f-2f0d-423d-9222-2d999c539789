from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class FileInfo(BaseModel):
    filename: str
    size: int
    duration: Optional[float] = None
    mime_type: str

class SrtFileInfo(BaseModel):
    filename: str
    download_url: str
    content_type: str
    file_size: int
    duration: Optional[float] = None
    is_base64_encoded: bool = False

class TranscribeResponse(BaseModel):
    task_id: str
    status: TaskStatus
    message: str
    file_info: Optional[FileInfo] = None

class TaskStatusResponse(BaseModel):
    task_id: str
    status: TaskStatus
    progress: int = Field(..., ge=0, le=100)
    message: str
    result: Optional[Dict[str, Any]] = None
    srt_files: Optional[List[SrtFileInfo]] = None
    error: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    processing_time: Optional[float] = None

class TranscriptionResult(BaseModel):
    language_code: str
    language_probability: float
    text: str
    words: List[Dict[str, Any]]
    additional_formats: Optional[List[Dict[str, Any]]] = None
