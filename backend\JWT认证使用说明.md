# JWT认证使用说明

## 📋 概述

本文档说明如何使用JWT认证功能来保护ElevenLabs STT Backend API。系统采用JWT代理认证机制，通过Worker验证用户身份并提供配额管理功能。

## 🔑 认证配置

### 环境变量配置

在 `.env` 文件中配置以下参数：

```bash
# JWT认证系统配置
WORKER_BASE_URL=https://your-worker.domain.com
WORKER_VERIFY_ENDPOINT=/api/auth/verify
DATABASE_URL=sqlite+aiosqlite:///./quota.db
ENABLE_QUOTA_SYSTEM=true

# 管理员API配置（可选）
ENABLE_ADMIN_API=true
ADMIN_USERS=555,admin_user,super_admin,manager_001
```

### 配置说明

- `WORKER_BASE_URL`: Worker服务的基础URL，用于JWT验证
- `WORKER_VERIFY_ENDPOINT`: Worker的JWT验证端点
- `ENABLE_QUOTA_SYSTEM`: 是否启用配额系统（必须为true才能使用JWT认证）
- `ENABLE_ADMIN_API`: 是否启用管理员API功能
- `ADMIN_USERS`: 管理员用户列表（逗号分隔）

## 🛡️ 受保护的端点

以下API端点需要提供有效的JWT token：

### 转录相关API
- `POST /api/v1/transcribe/upload` - 上传音频文件
- `GET /api/v1/transcribe/status/{task_id}` - 查询任务状态
- `GET /api/v1/transcribe/download/{task_id}/{filename}` - 下载文件

### 配额相关API
- `GET /api/v1/quota/info` - 获取配额信息
- `POST /api/v1/quota/recharge` - 充值配额

### 管理员API（需要管理员JWT）
- `GET /api/v1/admin/stats/overview` - 系统概览统计
- `GET /api/v1/admin/cards` - 查看卡密列表
- `POST /api/v1/admin/cards` - 创建卡密
- `GET /api/v1/admin/users` - 查看用户列表

## 🔐 认证方式

### JWT Token认证

所有受保护的端点都使用JWT Bearer Token认证：

```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@audio.mp3"
```

### JWT认证流程

```
客户端请求 → 提取JWT Token → Worker验证 → 用户ID提取 → 配额检查 → 业务逻辑
```

1. **客户端发送请求**：在Authorization头中携带JWT token
2. **后端提取token**：从请求头中提取JWT token
3. **Worker验证**：向Worker发送验证请求
4. **用户识别**：Worker返回用户ID
5. **配额检查**：检查用户配额是否充足
6. **执行业务逻辑**：配额充足则执行相应操作

## 📚 认证相关API

### 获取认证信息（无需认证）

```bash
GET /api/v1/auth/info
```

响应示例：
```json
{
  "auth_enabled": true,
  "auth_method": "JWT",
  "auth_header": "Authorization: Bearer <jwt_token>",
  "quota_system_enabled": true,
  "admin_api_enabled": true,
  "message": "请在请求头中提供有效的JWT token以访问受保护的端点",
  "endpoints": {
    "upload": "POST /api/v1/transcribe/upload",
    "status": "GET /api/v1/transcribe/status/{task_id}",
    "download": "GET /api/v1/transcribe/download/{task_id}/{filename}",
    "quota_info": "GET /api/v1/quota/info",
    "quota_recharge": "POST /api/v1/quota/recharge"
  }
}
```

### 获取配额信息（需要JWT认证）

```bash
GET /api/v1/quota/info
Authorization: Bearer <jwt_token>
```

响应示例：
```json
{
  "user_id": "555",
  "balance": 3600,
  "total_used": 1200,
  "total_recharged": 4800,
  "last_recharge_at": "2024-01-01T12:00:00Z",
  "created_at": "2024-01-01T10:00:00Z"
}
```

### 充值配额（需要JWT认证）

```bash
POST /api/v1/quota/recharge
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "card_id": "CARD123456789"
}
```

响应示例：
```json
{
  "success": true,
  "message": "充值成功",
  "recharged_amount": 1800,
  "new_balance": 5400,
  "card_info": {
    "card_id": "CARD123456789",
    "amount": 1800,
    "used_at": "2024-01-01T12:30:00Z"
  }
}
```

## 🚨 错误处理

### JWT认证失败响应

状态码: `401 Unauthorized`

```json
{
  "detail": {
    "error": "认证失败",
    "message": "JWT token无效或已过期",
    "hint": "请重新登录获取有效的JWT token"
  }
}
```

### 缺少JWT Token响应

状态码: `401 Unauthorized`

```json
{
  "detail": {
    "error": "认证失败",
    "message": "缺少JWT token",
    "hint": "请在请求头中提供JWT token: Authorization: Bearer <token>"
  }
}
```

### 配额不足响应

状态码: `402 Payment Required`

```json
{
  "detail": {
    "error": "配额不足",
    "message": "当前配额余额不足，需要1800秒，当前余额600秒",
    "current_balance": 600,
    "required_duration": 1800,
    "hint": "请充值后再试"
  }
}
```

### 权限不足响应（管理员API）

状态码: `403 Forbidden`

```json
{
  "detail": {
    "error": "权限不足",
    "message": "用户 555 不是管理员",
    "hint": "请使用管理员账户登录"
  }
}
```

## 🧪 测试JWT认证功能

### 测试认证信息获取

```bash
curl http://localhost:8000/api/v1/auth/info
```

### 测试配额信息获取

```bash
curl -H "Authorization: Bearer <your_jwt_token>" \
     http://localhost:8000/api/v1/quota/info
```

### 测试音频转录

```bash
curl -X POST http://localhost:8000/api/v1/transcribe/upload \
  -H "Authorization: Bearer <your_jwt_token>" \
  -F "file=@audio.mp3" \
  -F "language_code=zh"
```

### 运行测试脚本

```bash
cd backend
python test_quota_system.py  # 测试配额系统
python test_api_integration.py  # 测试API集成
```

## 🔧 开发指南

### 禁用JWT认证（开发环境）

在 `.env` 文件中设置：

```bash
ENABLE_QUOTA_SYSTEM=false
```

**注意**: 禁用配额系统后，所有接口将使用默认用户，无需JWT认证。

### Worker验证端点配置

确保Worker服务提供JWT验证端点：

```bash
# Worker验证端点
POST /api/auth/verify
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 在代码中使用JWT认证

```python
from fastapi import Depends
from app.auth.jwt_dependencies import verify_jwt_and_check_quota

@router.post("/protected-endpoint")
async def protected_function(
    auth_info = Depends(verify_jwt_and_check_quota)
):
    user_id, quota_service = auth_info
    # 此端点需要有效的JWT token和配额检查
    return {"message": "访问成功", "user_id": user_id}
```

## 🛠️ 架构设计

### 模块结构

```
app/auth/
├── __init__.py              # 模块导出
├── jwt_dependencies.py      # JWT认证依赖注入
└── admin_dependencies.py    # 管理员认证依赖注入

app/services/
├── quota_service.py         # 配额管理服务
└── transcription_task_service.py  # 转录任务服务（含配额）

app/models/
├── quota_models.py          # 配额相关数据模型
└── admin_models.py          # 管理员相关数据模型
```

### 设计原则

- **代理认证**: 通过Worker验证JWT，后端专注业务逻辑
- **配额管理**: 精确的配额预检查和使用记录
- **用户隔离**: 基于JWT用户ID实现数据隔离
- **权限分级**: 普通用户和管理员权限分离
- **可扩展性**: 支持多种配额计费模式

### JWT认证流程

```
[前端] → [后端API] → [Worker验证] → [配额检查] → [业务逻辑]
   ↓         ↓           ↓            ↓           ↓
JWT Token → 提取Token → 验证用户 → 检查配额 → 执行操作
```

## 🔒 安全建议

1. **HTTPS传输**: 生产环境必须使用HTTPS传输JWT token
2. **Token安全**: JWT token应设置合理的过期时间
3. **Worker安全**: 确保Worker验证端点的安全性
4. **配额控制**: 合理设置配额预检查阈值
5. **日志安全**: 避免在日志中记录完整JWT token
6. **环境隔离**: 不同环境使用不同的Worker服务

## 📝 更新日志

- **v2.0.0**: 统一JWT认证系统
  - 移除API密钥认证，统一使用JWT
  - 集成配额管理和用户隔离
  - 支持管理员权限分级
  - 完整的错误处理和配额控制
