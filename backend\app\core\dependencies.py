from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.task_service import TaskService
from app.services.file_service import FileService
from app.services.srt_service import SrtService
from app.services.quota_service import QuotaService

from app.core.database import get_db_session

# 服务依赖注入
def get_task_service() -> TaskService:
    """获取任务服务实例"""
    return TaskService()

def get_file_service() -> FileService:
    """获取文件服务实例"""
    return FileService()

def get_srt_service() -> SrtService:
    """获取SRT服务实例"""
    return SrtService()



async def get_quota_service(
    db_session: AsyncSession = Depends(get_db_session)
) -> QuotaService:
    """获取配额服务实例"""
    return QuotaService(db_session)

# 可以在这里添加其他依赖注入，比如数据库连接等
