#!/usr/bin/env python3
"""
基本功能测试脚本
用于验证后端服务的核心组件是否正常工作
"""

import sys
import os
import asyncio
import tempfile
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.request_models import TranscribeRequest, SrtExportOptions, TimestampsGranularity
from app.models.response_models import TaskStatus, FileInfo
from app.utils.validators import validate_file_format, validate_language_code
from app.utils.helpers import generate_task_id, format_file_size, format_duration

def test_models():
    """测试数据模型"""
    print("🧪 测试数据模型...")
    
    # 测试请求模型
    try:
        request = TranscribeRequest(
            language_code="zh",
            tag_audio_events=True,
            num_speakers=2,
            timestamps_granularity=TimestampsGranularity.WORD,
            additional_formats=[
                SrtExportOptions(
                    max_characters_per_line=50,
                    include_speakers=True
                )
            ]
        )
        print("✅ 请求模型创建成功")
        print(f"   语言代码: {request.language_code}")
        print(f"   时间戳粒度: {request.timestamps_granularity}")
        print(f"   额外格式: {len(request.additional_formats) if request.additional_formats else 0}")
    except Exception as e:
        print(f"❌ 请求模型测试失败: {e}")
        return False
    
    # 测试文件信息模型
    try:
        file_info = FileInfo(
            filename="test.mp3",
            size=1024000,
            duration=120.5,
            mime_type="audio/mp3"
        )
        print("✅ 文件信息模型创建成功")
        print(f"   文件名: {file_info.filename}")
        print(f"   大小: {format_file_size(file_info.size)}")
        print(f"   时长: {format_duration(file_info.duration)}")
    except Exception as e:
        print(f"❌ 文件信息模型测试失败: {e}")
        return False
    
    return True

def test_validators():
    """测试验证器"""
    print("\n🧪 测试验证器...")
    
    # 测试文件格式验证
    try:
        validate_file_format("test.mp3")
        print("✅ MP3 文件格式验证通过")
        
        validate_file_format("test.wav")
        print("✅ WAV 文件格式验证通过")
        
        try:
            validate_file_format("test.txt")
            print("❌ TXT 文件格式应该被拒绝")
            return False
        except:
            print("✅ TXT 文件格式正确被拒绝")
    except Exception as e:
        print(f"❌ 文件格式验证测试失败: {e}")
        return False
    
    # 测试语言代码验证
    try:
        validate_language_code("zh")
        print("✅ 中文语言代码验证通过")
        
        validate_language_code("en")
        print("✅ 英文语言代码验证通过")
        
        validate_language_code(None)
        print("✅ 空语言代码验证通过")
        
        try:
            validate_language_code("invalid")
            print("❌ 无效语言代码应该被拒绝")
            return False
        except:
            print("✅ 无效语言代码正确被拒绝")
    except Exception as e:
        print(f"❌ 语言代码验证测试失败: {e}")
        return False
    
    return True

def test_helpers():
    """测试工具函数"""
    print("\n🧪 测试工具函数...")
    
    try:
        # 测试任务ID生成
        task_id = generate_task_id()
        print(f"✅ 任务ID生成成功: {task_id[:8]}...")
        
        # 测试文件大小格式化
        size_str = format_file_size(1024000)
        print(f"✅ 文件大小格式化: {size_str}")
        
        # 测试时长格式化
        duration_str = format_duration(125.5)
        print(f"✅ 时长格式化: {duration_str}")
        
        duration_str_none = format_duration(None)
        print(f"✅ 空时长格式化: {duration_str_none}")
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False
    
    return True

async def test_services():
    """测试服务组件"""
    print("\n🧪 测试服务组件...")
    
    try:
        from app.services.file_service import FileService
        from app.core.config import settings
        
        # 创建临时目录用于测试
        with tempfile.TemporaryDirectory() as temp_dir:
            # 临时修改配置
            original_upload_dir = settings.UPLOAD_DIR
            settings.UPLOAD_DIR = temp_dir
            
            file_service = FileService()
            print("✅ 文件服务创建成功")
            
            # 测试MIME类型检测
            mime_type = file_service._detect_mime_type("test.mp3")
            print(f"✅ MIME类型检测: {mime_type}")
            
            # 恢复原始配置
            settings.UPLOAD_DIR = original_upload_dir
            
    except Exception as e:
        print(f"❌ 服务组件测试失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n🧪 测试配置...")
    
    try:
        from app.core.config import settings
        
        print(f"✅ 配置加载成功")
        print(f"   应用名称: {settings.APP_NAME}")
        print(f"   应用版本: {settings.APP_VERSION}")
        print(f"   Redis URL: {settings.REDIS_URL}")
        print(f"   最大上传大小: {format_file_size(settings.MAX_UPLOAD_SIZE)}")
        print(f"   最大并发任务: {settings.MAX_CONCURRENT_TASKS}")
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False
    
    return True

async def main():
    """主测试函数"""
    print("🚀 开始后端基本功能测试\n")
    
    tests = [
        ("数据模型", test_models),
        ("验证器", test_validators),
        ("工具函数", test_helpers),
        ("配置", test_config),
        ("服务组件", test_services),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {name} 测试通过")
            else:
                print(f"❌ {name} 测试失败")
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基本功能测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    asyncio.run(main())
