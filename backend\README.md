# ElevenLabs STT Backend

基于 FastAPI 的 ElevenLabs Speech-to-Text API 后端服务。

## 🚀 快速开始

> 📖 **详细启动指南**: [快速启动指南.md](./快速启动指南.md) - 包含完整的启动步骤、故障排除和最佳实践

### ⚡ 5分钟快速启动

#### Docker方式（推荐）
```bash
# 1. 克隆项目
git clone <repository-url>
cd speech-to-text/backend

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，至少修改 API_KEY 和 WORKER_BASE_URL

# 3. 启动服务
docker-compose up -d

# 4. 验证启动
curl http://localhost:8000/health
```

#### 本地开发方式
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env

# 3. 启动Redis
redis-server

# 4. 启动应用
python start_server.py
```

### 📋 系统要求

- **Python**: 3.11+
- **Docker**: 20.10+ 和 Docker Compose 2.0+
- **Redis**: 7.0+ (Docker方式自动安装)

### 🔧 启动方式

#### 专用启动脚本
```bash
# 基础开发启动
python start_server.py

# 带认证信息显示
python start_with_auth.py

# 直接启动
python -m app.main
```

#### Docker部署
```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f api

# 停止服务
docker-compose down
```

## 📋 API 文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 🔐 JWT认证系统

本服务采用JWT认证来保护API端点，提供用户隔离和配额管理功能。

#### 认证配置

在 `.env` 文件中配置：
```bash
# JWT认证系统配置
WORKER_BASE_URL=https://your-worker.domain.com
WORKER_VERIFY_ENDPOINT=/api/auth/verify
ENABLE_QUOTA_SYSTEM=true

# 管理员API配置（可选）
ENABLE_ADMIN_API=true
ADMIN_USERS=555,admin_user,super_admin
```

#### 认证方式

使用JWT Bearer Token认证：

```bash
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  http://localhost:8000/api/v1/transcribe/upload
```

#### JWT认证流程

```
客户端 → 后端API → Worker验证 → 配额检查 → 业务逻辑
```

1. 客户端在请求头中携带JWT token
2. 后端向Worker发送验证请求
3. Worker返回用户ID
4. 检查用户配额是否充足
5. 执行相应的业务逻辑

详细说明请参考：[JWT认证使用说明.md](./JWT认证使用说明.md)

### 🛡️ 管理员API

本服务提供完整的管理员API，支持卡密管理、用户管理和系统统计功能。

#### 管理员认证配置

在 `.env` 文件中配置：
```bash
# 启用管理员API
ENABLE_ADMIN_API=true
# 管理员用户列表（逗号分隔）
ADMIN_USERS=555,admin_user,super_admin,manager_001
```

#### 管理员API端点

**卡密管理**:
- `GET /api/v1/admin/cards` - 查看卡密列表
- `POST /api/v1/admin/cards` - 创建卡密
- `POST /api/v1/admin/cards/batch` - 批量创建卡密

**用户管理**:
- `GET /api/v1/admin/users` - 查看用户列表
- `PATCH /api/v1/admin/users/{user_id}/quota` - 修改用户配额

**统计信息**:
- `GET /api/v1/admin/stats/overview` - 系统概览统计
- `GET /api/v1/admin/stats/admin-info` - 管理员信息

**使用示例**:
```bash
# 查看系统统计（需要管理员JWT认证）
curl -X GET http://localhost:8000/api/v1/admin/stats/overview \
  -H "Authorization: Bearer <admin_jwt_token>"
```

详细说明请参考：[管理员API接口文档.md](./管理员API接口文档.md)

### 主要接口

#### 1. 上传音频文件（🛡️ 需要认证）
```
POST /api/v1/transcribe/upload
```

支持的参数：
- `file`: 音频/视频文件（必需）
- `language_code`: 语言代码（可选，如: zh, en, ja）
- `tag_audio_events`: 是否标记音频事件（默认: true）
- `num_speakers`: 最大说话人数量（可选，1-32）
- `timestamps_granularity`: 时间戳粒度（默认: word）
- `additional_formats`: SRT字幕文件生成配置（可选，JSON字符串）

**注意**: 说话人识别(diarize)功能已固定启用，无需手动设置。


#### additional_formats 参数详解

`additional_formats` 参数用于配置SRT字幕文件的生成选项，格式为JSON字符串数组。

**参数格式：**
```json
[{
  "format": "srt",
  "max_characters_per_line": 50,
  "include_speakers": false,
  "include_timestamps": true,
  "segment_on_silence_longer_than_s": 0.6,
  "max_segment_duration_s": 5.0,
  "max_segment_chars": 90
}]
```

**参数说明：**
- `format`: 输出格式，固定为 "srt"
- `max_characters_per_line`: 每行最大字符数（范围: 1-200，默认: 50）
- `include_speakers`: 是否包含说话人标记（默认: false）
- `include_timestamps`: 是否包含时间戳（默认: true）
- `segment_on_silence_longer_than_s`: 静音分段阈值秒数（范围: 0.1-10.0，默认: 0.6）
- `max_segment_duration_s`: 最大段落时长秒数（范围: 1.0-30.0，默认: 5.0）
- `max_segment_chars`: 最大段落字符数（范围: 10-500，默认: 90）

**使用示例：**

**基本SRT生成：**
```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -H "Authorization: Bearer OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310" \
  -F "file=@audio.mp3" \
  -F 'additional_formats=[{"format": "srt"}]'
```

**自定义SRT配置：**
```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -H "Authorization: Bearer OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310" \
  -F "file=@audio.mp3" \
  -F 'additional_formats=[{"format": "srt", "max_characters_per_line": 40, "include_speakers": true}]'
```

**JavaScript前端示例：**
```javascript
const formData = new FormData();
formData.append('file', audioFile);
formData.append('additional_formats', JSON.stringify([{
  "format": "srt",
  "max_characters_per_line": 50,
  "include_speakers": false,
  "include_timestamps": true
}]));

fetch('/api/v1/transcribe/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310'
  },
  body: formData
});
```

**重要提示：**
- 如果不提供 `additional_formats` 参数，将不会生成SRT文件
- 响应中的 `srt_files` 字段将包含生成的SRT文件信息
- 生成的SRT文件可通过下载API获取

#### 2. 查询任务状态（🛡️ 需要JWT认证）
```
GET /api/v1/transcribe/status/{task_id}
```

#### 3. 下载SRT文件（🛡️ 需要JWT认证）
```
GET /api/v1/transcribe/download/{task_id}/{filename}
```

#### 4. 配额相关接口

**获取配额信息（🛡️ 需要JWT认证）**
```
GET /api/v1/quota/info
```

**充值配额（🛡️ 需要JWT认证）**
```
POST /api/v1/quota/recharge
```

#### 5. 认证相关接口

**获取认证信息（无需认证）**
```
GET /api/v1/auth/info
```

**参数说明**:
- `task_id`: 转录任务ID
- `filename`: SRT文件名（从任务状态响应中获取）

**使用示例**:
```bash
# 1. 先查询任务状态获取下载信息
curl "http://localhost:8000/api/v1/transcribe/status/your-task-id"

# 2. 使用返回的下载URL下载SRT文件
curl -O "http://localhost:8000/api/v1/transcribe/download/your-task-id/your-task-id.srt"
```

#### 4. 健康检查
```
GET /health
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `REDIS_URL` | Redis连接URL | `redis://localhost:6379/0` |
| `MAX_UPLOAD_SIZE` | 最大文件大小（字节） | `**********` (1GB) |
| `MAX_CONCURRENT_TASKS` | 最大并发任务数 | `3` |
| `LOG_LEVEL` | 日志级别 | `INFO` |

### 支持的文件格式

- 音频: MP3, WAV, FLAC, M4A, AAC, OGG
- 视频: MP4, MOV

### 支持的语言

zh, en, ja, ko, es, fr, de, ru, it, pt

### 说话人识别功能

说话人识别（Speaker Diarization）功能可以识别音频中的不同说话人，并在字幕中标记说话人信息。

#### 功能特点
- **默认启用**: 说话人识别功能已固定启用，无需手动设置
- **多人对话**: 适用于访谈、会议、播客等多人场景
- **标记格式**: 在字幕中显示 `[speaker_0]`, `[speaker_1]` 等标记
- **智能识别**: 自动检测音频中的不同说话人

#### 使用示例

**基本使用**:
```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -H "Authorization: Bearer OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310" \
  -F "file=@audio.mp3" \
  -F "num_speakers=2" \
  -F 'additional_formats=[{"format": "srt", "include_speakers": true}]'
```

**输出示例**:
```srt
1
00:00:00,119 --> 00:00:04,999
[speaker_0] When I woke up, sweating from the pain...

2
00:00:05,099 --> 00:00:09,939
[speaker_1] As a woman, you didn't even know...
```

#### 参数说明

- `num_speakers`: 可选参数，指定最大说话人数量（1-32）
- 如果不指定 `num_speakers`，系统会自动检测说话人数量
- 说话人识别功能始终启用，确保最佳的转录效果

### 完整工作流程

#### 1. 上传音频文件
```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -H "Authorization: Bearer OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310" \
  -F "file=@audio.mp3" \
  -F 'additional_formats=[{"format": "srt"}]'
```

**响应示例**:
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "pending",
  "message": "任务已创建，开始处理...",
  "file_info": {
    "filename": "audio.mp3",
    "size": 1024000,
    "duration": 120.5,
    "mime_type": "audio/mpeg"
  }
}
```

#### 2. 查询处理状态
```bash
curl "http://localhost:8000/api/v1/transcribe/status/abc123-def456-ghi789"
```

**响应示例**（处理完成）:
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "completed",
  "progress": 100,
  "message": "转录完成",
  "srt_files": [
    {
      "filename": "abc123-def456-ghi789.srt",
      "download_url": "/api/v1/transcribe/download/abc123-def456-ghi789/abc123-def456-ghi789.srt",
      "content_type": "text/srt",
      "file_size": 2048,
      "duration": 120.5
    }
  ],
  "processing_time": 45.2
}
```

#### 3. 下载SRT文件
```bash
# 方法1: 直接下载
curl -O "http://localhost:8000/api/v1/transcribe/download/abc123-def456-ghi789/abc123-def456-ghi789.srt"

# 方法2: 指定输出文件名
curl "http://localhost:8000/api/v1/transcribe/download/abc123-def456-ghi789/abc123-def456-ghi789.srt" \
  -o "my_subtitles.srt"
```

## 🛠️ 开发指南

### 项目结构

```
app/
├── __init__.py
├── main.py                 # FastAPI 应用入口
├── models/
│   ├── request_models.py   # 请求模型定义
│   └── response_models.py  # 响应模型定义
├── services/
│   ├── elevenlabs_client.py # ElevenLabs API 客户端
│   ├── file_service.py     # 文件处理服务
│   └── task_service.py     # 任务管理服务
├── api/
│   └── transcribe.py       # 转录API路由
├── core/
│   ├── config.py           # 配置管理
│   └── dependencies.py     # 依赖注入
└── utils/
    ├── validators.py       # 参数验证
    └── helpers.py          # 工具函数
```

### 添加新功能

1. 在相应的模块中添加代码
2. 更新模型定义（如需要）
3. 添加测试
4. 更新文档

### 最近更新

#### v1.2.0 - 说话人识别固定启用 + 文档完善
- ✅ 说话人识别功能固定启用，确保最佳转录效果
- ✅ 移除 `diarize` 参数，简化API使用
- ✅ 移除 `timestamps_granularity` 参数，提高兼容性
- ✅ 新增API密钥认证功能，提升安全性
- ✅ 完善 `additional_formats` 参数文档，包含详细说明和示例
- ✅ 新增SRT文件生成故障排除指南

#### v1.1.0 - 说话人识别可选控制（已废弃）
- ❌ 已移除 `diarize` 参数的可选控制
- ❌ 说话人识别现在固定启用

## 🚨 故障排除

### 常见问题

1. **Redis 连接失败**
   ```bash
   # 检查 Redis 是否运行
   docker-compose ps redis
   
   # 查看 Redis 日志
   docker-compose logs redis
   ```

2. **文件上传失败**
   - 检查文件大小是否超过限制
   - 验证文件格式是否支持
   - 确认磁盘空间充足

3. **转录任务超时**
   - 检查网络连接
   - 增加超时时间设置
   - 查看 ElevenLabs API 状态

4. **说话人识别问题**
   - 说话人识别功能已固定启用，无需设置参数
   - 多人音频可设置 `num_speakers` 参数提高准确性
   - 检查音频质量，背景噪音可能影响识别效果
   - 单人音频也会显示 `[speaker_0]` 标记，这是正常行为

5. **SRT文件生成问题**
   - 确认请求中包含了 `additional_formats` 参数
   - 检查参数格式：`[{"format": "srt"}]`
   - 如果 `srt_files` 为空，说明没有提供 `additional_formats` 参数
   - 验证JSON格式是否正确，避免解析错误

5. **SRT文件下载问题**
   - 确认任务状态为 `completed` 才能下载
   - 检查下载URL中的 `task_id` 和 `filename` 是否正确
   - 验证文件是否存在：查看任务状态响应中的 `srt_files` 字段
   - 下载链接有效期：文件会在一定时间后自动清理

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f api

# 查看特定时间的日志
docker-compose logs --since="2024-01-01T00:00:00" api
```

## 📊 监控

### 健康检查

```bash
curl http://localhost:8000/health
```

### 性能监控

应用包含以下监控指标：
- 请求响应时间
- 任务处理状态
- Redis 连接状态
- 文件上传统计

## 🔒 安全

- 文件类型验证
- 文件大小限制
- 请求频率限制
- CORS 配置
- 输入参数验证

## 📄 许可证

MIT License
