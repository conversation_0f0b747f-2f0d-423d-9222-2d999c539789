#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试说话人识别功能的开关控制
"""

import pytest
from app.models.request_models import TranscribeRequest, TimestampsGranularity


def test_diarize_default_false():
    """测试diarize参数默认值为False"""
    request = TranscribeRequest()
    assert request.diarize == False


def test_diarize_can_be_set_true():
    """测试diarize参数可以设置为True"""
    request = TranscribeRequest(diarize=True)
    assert request.diarize == True


def test_diarize_can_be_set_false():
    """测试diarize参数可以设置为False"""
    request = TranscribeRequest(diarize=False)
    assert request.diarize == False


def test_complete_request_with_diarize():
    """测试包含diarize参数的完整请求"""
    request = TranscribeRequest(
        language_code="zh",
        tag_audio_events=True,
        diarize=True,
        num_speakers=2,
        timestamps_granularity=TimestampsGranularity.WORD
    )
    
    assert request.language_code == "zh"
    assert request.tag_audio_events == True
    assert request.diarize == True
    assert request.num_speakers == 2
    assert request.timestamps_granularity == TimestampsGranularity.WORD


def test_request_serialization():
    """测试请求序列化包含diarize参数"""
    request = TranscribeRequest(diarize=True)
    data = request.model_dump()
    
    assert "diarize" in data
    assert data["diarize"] == True


if __name__ == "__main__":
    # 运行基本测试
    test_diarize_default_false()
    test_diarize_can_be_set_true()
    test_diarize_can_be_set_false()
    test_complete_request_with_diarize()
    test_request_serialization()
    
    print("✅ 所有测试通过！说话人识别功能开关控制正常工作。")
