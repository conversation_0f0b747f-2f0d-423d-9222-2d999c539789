#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JWT认证功能测试脚本

测试JWT认证功能是否正常工作。
"""

import asyncio
import aiohttp
import json

# 测试配置
BASE_URL = "http://localhost:8000"
# 注意：这是一个过期的测试JWT token，实际使用时需要有效的token
TEST_JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTUiLCJleHAiOjE3NTQ0MDEzOTEwMzQsInR5cGUiOiJhY2Nlc3MifQ==.ZZa9CO4q16Vq7HHFING83NtK0DZufrDT4LcH2WI+zS4="
INVALID_JWT_TOKEN = "invalid.jwt.token"


async def test_auth_info():
    """测试获取认证信息（无需认证）"""
    print("🔍 测试认证信息获取...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/auth/info") as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def test_quota_info_valid():
    """测试有效JWT token获取配额信息"""
    print("✅ 测试有效JWT token获取配额信息...")
    
    headers = {"Authorization": f"Bearer {TEST_JWT_TOKEN}"}
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/quota/info", headers=headers) as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def test_quota_info_invalid():
    """测试无效JWT token获取配额信息"""
    print("❌ 测试无效JWT token获取配额信息...")
    
    headers = {"Authorization": f"Bearer {INVALID_JWT_TOKEN}"}
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/quota/info", headers=headers) as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def test_quota_info_missing():
    """测试缺少JWT token的配额查询"""
    print("🚫 测试缺少JWT token的配额查询...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/quota/info") as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def test_transcribe_status_with_jwt():
    """测试带JWT token的任务状态查询"""
    print("🔑 测试带JWT token的任务状态查询...")
    
    headers = {"Authorization": f"Bearer {TEST_JWT_TOKEN}"}
    
    # 测试一个不存在的任务ID
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/transcribe/status/test-task-id", headers=headers) as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def test_transcribe_status_without_jwt():
    """测试不带JWT token的任务状态查询"""
    print("🔓 测试不带JWT token的任务状态查询...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/transcribe/status/test-task-id") as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def test_admin_api_with_jwt():
    """测试管理员API（带JWT token）"""
    print("👑 测试管理员API（带JWT token）...")
    
    headers = {"Authorization": f"Bearer {TEST_JWT_TOKEN}"}
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/admin/stats/admin-info", headers=headers) as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def test_health_check():
    """测试健康检查（无需认证）"""
    print("💚 测试健康检查...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/health") as response:
            data = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print()


async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 JWT认证功能测试")
    print("=" * 60)
    print()
    
    try:
        # 健康检查
        await test_health_check()
        
        # 认证信息测试
        await test_auth_info()
        
        # 配额信息测试
        await test_quota_info_valid()
        await test_quota_info_invalid()
        await test_quota_info_missing()
        
        # 转录状态查询测试
        await test_transcribe_status_with_jwt()
        await test_transcribe_status_without_jwt()
        
        # 管理员API测试
        await test_admin_api_with_jwt()
        
        print("=" * 60)
        print("✅ JWT认证功能测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
