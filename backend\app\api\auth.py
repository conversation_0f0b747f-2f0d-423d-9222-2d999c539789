# -*- coding: utf-8 -*-
"""
认证相关API端点
"""

from fastapi import APIRouter
from typing import Dict, Any

from app.core.config import settings

router = APIRouter(prefix="/api/v1/auth", tags=["认证"])


@router.get("/info")
async def get_auth_info() -> Dict[str, Any]:
    """
    获取认证配置信息（无需认证）

    Returns:
        Dict: 认证配置信息
    """
    return {
        "auth_enabled": True,
        "auth_method": "JWT",
        "auth_header": "Authorization: Bearer <jwt_token>",
        "quota_system_enabled": settings.ENABLE_QUOTA_SYSTEM,
        "admin_api_enabled": settings.ENABLE_ADMIN_API,
        "message": "请在请求头中提供有效的JWT token以访问受保护的端点",
        "endpoints": {
            "upload": "POST /api/v1/transcribe/upload",
            "status": "GET /api/v1/transcribe/status/{task_id}",
            "download": "GET /api/v1/transcribe/download/{task_id}/{filename}",
            "quota_info": "GET /api/v1/quota/info",
            "quota_recharge": "POST /api/v1/quota/recharge"
        }
    }


