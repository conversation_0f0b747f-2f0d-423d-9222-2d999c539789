#!/usr/bin/env python3
"""
SRT处理功能测试脚本
测试从ElevenLabs API响应中提取和处理SRT文件
"""

import sys
import os
import asyncio
import json
import tempfile

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.srt_service import SrtService

# 模拟ElevenLabs API响应数据
MOCK_API_RESPONSE = {
    "language_code": "eng",
    "language_probability": 1.0,
    "text": "He furrowed his brows and said, \"Try to spruce up a bit when you go out next time.\" Unperturbed, I continued to engage with my phone, leaving <PERSON> to awkwardly attempt small talk for the rest of the journey.",
    "words": [
        {
            "text": "He",
            "start": 0.079,
            "end": 0.199,
            "type": "word",
            "speaker_id": "speaker_0",
            "logprob": 0.0
        }
    ],
    "additional_formats": [
        {
            "requested_format": "srt",
            "file_extension": "srt",
            "content_type": "text/srt",
            "is_base64_encoded": False,
            "content": """1
00:00:00,079 --> 00:00:03,979
[speaker_0] He furrowed his brows and said, "Try
to spruce up a bit when you go out next time."

2
00:00:04,139 --> 00:00:08,619
Unperturbed, I continued to engage with my phone,
leaving Leon to awkwardly attempt small

3
00:00:08,679 --> 00:00:12,939
talk for the rest of the journey. Upon arriving at
the venue, Mark quickly came over to

4
00:00:12,960 --> 00:00:17,379
explain why he had to draft Leon in as a
groomsman, assuring me that Leon would have

5
00:00:17,399 --> 00:00:21,199
brought me along otherwise. He downed three
penalty drinks to show his regret.

6
00:00:21,439 --> 00:00:24,979
I wasn't at all upset. Don't blame yourself. Happy
wedding.

7
00:00:25,239 --> 00:00:29,879
While everyone was initially shocked not to see me
make a scene as before, I acted as if

8
00:00:29,899 --> 00:00:33,539
I was an entirely different person, disinterested
in their surprise.

9
00:00:33,899 --> 00:00:35,920
Soon, the atmosphere livened up again.

10
00:00:36,180 --> 00:00:40,880
Someone wanted to make Zania drink, and she looked
pitifully at Leon, who promptly

11
00:00:40,959 --> 00:00:42,500
stepped in to shield her from the drink.

12
00:00:42,860 --> 00:00:47,759
Observing the familiar yet alien Leon, I realized
I might never have truly known

13
00:00:47,759 --> 00:00:52,359
him. On our third anniversary during dinner, one
of his friends insisted I drink despite

14
00:00:52,379 --> 00:00:57,019
my protests of feeling unwell. Leon, thinking I
was just throwing a temper tantrum,

15
00:00:57,199 --> 00:01:00,939
forced me to drink. I ended up in the hospital
that night, where I learned I had

16
00:01:01,000 --> 00:01:01,659
miscarried."""
        }
    ]
}

async def test_srt_processing():
    """测试SRT处理功能"""
    print("🧪 测试SRT处理功能...")
    
    # 创建临时目录用于测试
    with tempfile.TemporaryDirectory() as temp_dir:
        # 临时修改配置
        from app.core.config import settings
        original_upload_dir = settings.UPLOAD_DIR
        settings.UPLOAD_DIR = temp_dir
        
        try:
            srt_service = SrtService()
            task_id = "test-task-123"
            
            print(f"📁 临时目录: {temp_dir}")
            print(f"🆔 测试任务ID: {task_id}")
            
            # 测试处理additional_formats
            result = await srt_service.process_additional_formats(task_id, MOCK_API_RESPONSE)
            
            print("\n📊 处理结果:")
            print(f"   转录数据: {'✅ 存在' if result.get('transcription') else '❌ 缺失'}")
            print(f"   SRT文件数量: {len(result.get('srt_files', []))}")
            
            if result.get('srt_files'):
                srt_file = result['srt_files'][0]
                print(f"\n📄 SRT文件信息:")
                print(f"   文件名: {srt_file['filename']}")
                print(f"   下载URL: {srt_file['download_url']}")
                print(f"   内容类型: {srt_file['content_type']}")
                print(f"   文件大小: {srt_file['file_size']} 字节")
                print(f"   时长: {srt_file['duration']:.2f} 秒" if srt_file['duration'] else "   时长: 未知")
                
                # 验证文件是否真的被创建
                file_path = srt_file['file_path']
                if os.path.exists(file_path):
                    print(f"✅ SRT文件已创建: {file_path}")
                    
                    # 读取文件内容验证
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    print(f"📝 文件内容预览 (前200字符):")
                    print(f"   {content[:200]}...")
                    
                    # 测试统计功能
                    stats = srt_service.get_srt_statistics(content)
                    print(f"\n📈 SRT统计信息:")
                    print(f"   字幕条数: {stats['subtitle_count']}")
                    print(f"   总字符数: {stats['total_characters']}")
                    print(f"   平均每条字符数: {stats['average_chars_per_subtitle']:.1f}")
                    print(f"   总时长: {stats['duration']:.2f} 秒" if stats['duration'] else "   总时长: 未知")
                    
                else:
                    print(f"❌ SRT文件未创建: {file_path}")
                    return False
                
                # 测试文件获取功能
                retrieved_path = await srt_service.get_srt_file(task_id, srt_file['filename'])
                if retrieved_path:
                    print(f"✅ 文件获取功能正常: {retrieved_path}")
                else:
                    print(f"❌ 文件获取功能失败")
                    return False
                
            else:
                print("❌ 没有生成SRT文件")
                return False
            
            print("\n🎉 SRT处理功能测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ SRT处理测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 恢复原始配置
            settings.UPLOAD_DIR = original_upload_dir

async def test_srt_time_parsing():
    """测试SRT时间解析功能"""
    print("\n🧪 测试SRT时间解析功能...")
    
    srt_service = SrtService()
    
    test_cases = [
        ("00:00:30,500", 30.5),
        ("00:01:15,250", 75.25),
        ("01:30:45,750", 5445.75),
        ("00:00:00,000", 0.0)
    ]
    
    all_passed = True
    
    for time_str, expected in test_cases:
        result = srt_service._parse_srt_time(time_str)
        if abs(result - expected) < 0.001:
            print(f"✅ {time_str} -> {result}s (期望: {expected}s)")
        else:
            print(f"❌ {time_str} -> {result}s (期望: {expected}s)")
            all_passed = False
    
    if all_passed:
        print("✅ SRT时间解析功能测试通过！")
    else:
        print("❌ SRT时间解析功能测试失败！")
    
    return all_passed

async def main():
    """主测试函数"""
    print("🚀 开始SRT处理功能测试\n")
    
    tests = [
        ("SRT处理功能", test_srt_processing),
        ("SRT时间解析", test_srt_time_parsing),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {name} 测试通过\n")
            else:
                print(f"❌ {name} 测试失败\n")
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}\n")
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有SRT处理功能测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    asyncio.run(main())
