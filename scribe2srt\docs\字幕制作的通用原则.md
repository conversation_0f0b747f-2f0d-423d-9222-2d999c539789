# 字幕制作的通用原则

## 概述

本文档总结了字幕制作的通用原则和最佳实践，这些原则被广泛应用于电影、电视、流媒体平台等各种媒体内容的字幕制作中。

## 基本原则

### 1. 时长控制
- **最短显示时间**：0.83秒（Netflix标准：5/6秒）
- **最长显示时间**：7.0秒
- **最小间隔**：0.083秒（约2帧@24fps）

### 2. 字符密度控制（CPS - Characters Per Second）
- **中文/日文/韩文**：每秒最多15个字符
- **拉丁语言**：每秒最多15个字符
- **动态调整**：对于极短文本允许更高的CPS

### 3. 每行字符数限制（CPL - Characters Per Line）
- **中文/日文/韩文**：每行最多25个字符
- **拉丁语言**：每行最多42个字符

### 4. 行数限制
- **最大行数**：2行
- **优先单行显示**：当文本较短时优先使用单行

## 分割原则

### 1. 语义完整性
- 优先保持句子的完整性
- 避免在句子中间分割
- 保持语法结构的连贯性

### 2. 标点符号优先级
- **高优先级**：句号(。)、感叹号(！)、问号(？)
- **中优先级**：分号(；)、冒号(：)
- **低优先级**：逗号(，)、顿号(、)

### 3. 换行规则
- 在标点符号处换行
- 避免在连词、介词后换行
- 保持语义单元的完整性

## 质量标准

### 1. 合规率目标
- **整体合规率**：≥85%
- **标点问题率**：≤3%
- **CPS超限率**：≤5%
- **时长问题率**：≤2%

### 2. 可读性要求
- 文本清晰易读
- 时间同步准确
- 语义表达完整

## 技术实现

### 1. 两阶段处理流程
1. **句子预分割**：基于标点符号优先级进行语义分割
2. **智能合并**：根据时长、CPS、CPL等规则进行优化合并

### 2. 多语言支持
- 针对不同语言特性调整参数
- 支持CJK和拉丁语言的差异化处理

### 3. 动态优化
- 根据文本长度动态调整CPS限制
- 智能收益计算指导合并决策

## 参考标准

本文档参考了以下行业标准：
- Netflix字幕制作标准
- BBC字幕制作指南
- 国际字幕协会(ESIST)标准
- 中国广播电视行业标准
