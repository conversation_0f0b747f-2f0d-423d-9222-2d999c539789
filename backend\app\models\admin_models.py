# -*- coding: utf-8 -*-
"""
管理员API相关数据模型
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

# ============================================================================
# 枚举类型
# ============================================================================

class CardStatus(str, Enum):
    """充值卡状态"""
    ALL = "all"
    UNUSED = "unused"
    USED = "used"

class UserStatus(str, Enum):
    """用户状态"""
    ACTIVE = "active"
    EXHAUSTED = "exhausted"

class QuotaOperation(str, Enum):
    """配额操作类型"""
    ADD = "add"
    SET = "set"
    SUBTRACT = "subtract"

# ============================================================================
# 请求模型
# ============================================================================

class CreateCardRequest(BaseModel):
    """创建充值卡请求"""
    quota_hours: float = Field(..., gt=0, le=1000, description="配额小时数")
    description: str = Field("", max_length=200, description="卡密描述")
    count: int = Field(1, ge=1, le=1, description="创建数量（单张）")

class BatchCreateCardRequest(BaseModel):
    """批量创建充值卡请求"""
    quota_hours: float = Field(..., gt=0, le=1000, description="配额小时数")
    description: str = Field("", max_length=200, description="卡密描述")
    count: int = Field(..., ge=1, le=1000, description="创建数量")

class CardListRequest(BaseModel):
    """卡密列表查询请求"""
    status: CardStatus = Field(CardStatus.ALL, description="卡密状态")
    limit: int = Field(50, ge=1, le=200, description="每页数量")
    offset: int = Field(0, ge=0, description="偏移量")
    search: Optional[str] = Field(None, max_length=100, description="搜索关键词")
    created_after: Optional[datetime] = Field(None, description="创建时间过滤")
    quota_min: Optional[float] = Field(None, ge=0, description="最小配额过滤")
    quota_max: Optional[float] = Field(None, ge=0, description="最大配额过滤")

class UserListRequest(BaseModel):
    """用户列表查询请求"""
    limit: int = Field(50, ge=1, le=200, description="每页数量")
    offset: int = Field(0, ge=0, description="偏移量")
    search: Optional[str] = Field(None, max_length=100, description="搜索用户ID")
    status: Optional[UserStatus] = Field(None, description="用户状态")

class ModifyUserQuotaRequest(BaseModel):
    """修改用户配额请求"""
    operation: QuotaOperation = Field(..., description="操作类型")
    quota_hours: float = Field(..., gt=0, le=10000, description="配额小时数")
    reason: str = Field("", max_length=200, description="操作原因")

# ============================================================================
# 响应模型
# ============================================================================

class CardInfo(BaseModel):
    """充值卡信息"""
    card_id: str = Field(..., description="卡密ID")
    quota_amount: int = Field(..., description="配额秒数")
    quota_hours: float = Field(..., description="配额小时数")
    description: str = Field(..., description="卡密描述")
    is_used: bool = Field(..., description="是否已使用")
    used_by: Optional[str] = Field(None, description="使用者")
    used_at: Optional[datetime] = Field(None, description="使用时间")
    created_at: datetime = Field(..., description="创建时间")

class CardSummary(BaseModel):
    """卡密统计信息"""
    total_cards: int = Field(..., description="总卡密数")
    unused_cards: int = Field(..., description="未使用卡密数")
    used_cards: int = Field(..., description="已使用卡密数")
    total_quota_seconds: int = Field(..., description="总配额秒数")
    used_quota_seconds: int = Field(..., description="已使用配额秒数")

class PaginationInfo(BaseModel):
    """分页信息"""
    total: int = Field(..., description="总数量")
    limit: int = Field(..., description="每页数量")
    offset: int = Field(..., description="偏移量")
    has_next: bool = Field(..., description="是否有下一页")

class CardListResponse(BaseModel):
    """卡密列表响应"""
    success: bool = Field(True, description="请求是否成功")
    data: Dict[str, Any] = Field(..., description="响应数据")

class UserInfo(BaseModel):
    """用户信息"""
    user_id: str = Field(..., description="用户ID")
    quota_balance: int = Field(..., description="剩余配额秒数")
    quota_balance_hours: float = Field(..., description="剩余配额小时数")
    total_quota: int = Field(..., description="总配额秒数")
    total_quota_hours: float = Field(..., description="总配额小时数")
    expiry_date: Optional[datetime] = Field(None, description="到期时间")
    status: str = Field(..., description="用户状态")
    last_updated: datetime = Field(..., description="最后更新时间")
    total_usage: int = Field(0, description="总使用秒数")
    total_usage_hours: float = Field(0, description="总使用小时数")
    recharge_count: int = Field(0, description="充值次数")
    last_activity: Optional[datetime] = Field(None, description="最后活动时间")

class UserSummary(BaseModel):
    """用户统计信息"""
    total_users: int = Field(..., description="总用户数")
    active_users: int = Field(..., description="活跃用户数")
    exhausted_users: int = Field(..., description="配额耗尽用户数")
    total_quota_distributed: int = Field(..., description="总分发配额")
    total_quota_used: int = Field(..., description="总使用配额")

class UserListResponse(BaseModel):
    """用户列表响应"""
    success: bool = Field(True, description="请求是否成功")
    data: Dict[str, Any] = Field(..., description="响应数据")

class SystemStats(BaseModel):
    """系统统计信息"""
    cards: Dict[str, Any] = Field(..., description="卡密统计")
    users: Dict[str, Any] = Field(..., description="用户统计")
    quota: Dict[str, Any] = Field(..., description="配额统计")
    activity: Dict[str, Any] = Field(..., description="活动统计")

class AdminResponse(BaseModel):
    """通用管理员API响应"""
    success: bool = Field(True, description="请求是否成功")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    message: Optional[str] = Field(None, description="响应消息")

# ============================================================================
# 使用日志模型
# ============================================================================

class UsageLogInfo(BaseModel):
    """使用日志信息"""
    log_id: int = Field(..., description="日志ID")
    user_id: str = Field(..., description="用户ID")
    task_id: str = Field(..., description="任务ID")
    quota_used: int = Field(..., description="使用配额秒数")
    quota_used_hours: float = Field(..., description="使用配额小时数")
    file_duration: Optional[int] = Field(None, description="文件时长秒数")
    created_at: datetime = Field(..., description="创建时间")

class UsageLogListRequest(BaseModel):
    """使用日志查询请求"""
    user_id: Optional[str] = Field(None, description="用户ID过滤")
    limit: int = Field(100, ge=1, le=1000, description="数量限制")
    offset: int = Field(0, ge=0, description="偏移量")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
