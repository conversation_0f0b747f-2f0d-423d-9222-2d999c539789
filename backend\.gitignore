# Python 编译文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 安装日志
pip-log.txt
pip-delete-this-directory.txt

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# 翻译文件
*.mo
*.pot

# Django/Flask 相关
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx 文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 项目特定文件
# 数据库文件
*.db
*.sqlite
*.sqlite3
quota.db

# 日志文件
logs/
*.log

# 上传文件目录
uploads/
chunks/
/tmp/

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 备份文件
*.bak
*.backup
*.orig

# 配置文件（保留示例文件）
.env
!.env.example

# 测试文件输出
test_output/
test_results/

# Docker 相关
.dockerignore

# SSL 证书
ssl/
*.pem
*.key
*.crt
*.cert

# Redis 数据
dump.rdb

# 缓存文件
.cache/
cache/

# 编辑器临时文件
*.sublime-project
*.sublime-workspace

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 媒体文件（测试用）
*.mp3
*.wav
*.mp4
*.avi
*.mov
*.flac
*.m4a
*.aac
*.ogg

# 输出文件
*.srt
*.vtt
*.json
!requirements.txt
!package.json

# 系统文件
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 性能分析文件
*.prof

# 覆盖率文件
.coverage.*
coverage.xml
htmlcov/

# 文档构建
docs/_build/
docs/build/

# 本地开发文件
local_settings.py
dev_settings.py

# 密钥文件
*.pem
*.key
secret_key.txt

# 数据文件
data/
datasets/

# 模型文件
models/
*.model
*.pkl
*.pickle

# 监控和分析
.monitoring/
.profiling/

# 第三方工具
.mypy_cache/
.pytest_cache/
.ruff_cache/

# 运行时文件
*.pid
*.sock

# 临时下载
downloads/

# 开发工具配置
.editorconfig
.flake8
.pylintrc
mypy.ini
setup.cfg
tox.ini

# 部署相关
deploy/
deployment/
k8s/
kubernetes/

# 环境特定
production.env
staging.env
development.env

# 备份和快照
backup/
snapshots/
*.snapshot

# 性能测试
benchmark/
load_test/

# 安全扫描结果
security_scan/
vulnerability_report/

# API 文档生成
api_docs/
openapi.json
swagger.json

# 监控数据
metrics/
monitoring/
alerts/

# 构建产物
build/
dist/
target/

# 包管理器锁文件（根据需要调整）
# poetry.lock
# Pipfile.lock

# 本地配置覆盖
local.env
override.env
