#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带JWT认证信息显示的启动脚本

显示JWT认证配置信息，方便开发和测试。
"""

import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings


def print_auth_info():
    """打印JWT认证配置信息"""
    print("🔐 JWT认证配置信息:")
    print(f"   配额系统: {'✅ 启用' if settings.ENABLE_QUOTA_SYSTEM else '❌ 禁用'}")
    print(f"   管理员API: {'✅ 启用' if settings.ENABLE_ADMIN_API else '❌ 禁用'}")
    print(f"   Worker地址: {settings.WORKER_BASE_URL}")
    print()

    if settings.ENABLE_QUOTA_SYSTEM:
        print("🔑 JWT认证系统:")
        print("   认证方式: Authorization: Bearer <jwt_token>")
        print("   Worker验证: " + settings.WORKER_BASE_URL)
        print()

        print("📚 认证相关API:")
        print("   GET  /api/v1/auth/info     - 获取认证信息（无需认证）")
        print("   GET  /api/v1/quota/info    - 获取配额信息（需要JWT认证）")
        print("   POST /api/v1/quota/recharge - 充值配额（需要JWT认证）")
        print()

        print("🛡️ 受保护的端点（需要JWT认证）:")
        print("   POST /api/v1/transcribe/upload")
        print("   GET  /api/v1/transcribe/status/{task_id}")
        print("   GET  /api/v1/transcribe/download/{task_id}/{filename}")
        print()

        if settings.ENABLE_ADMIN_API:
            print("👑 管理员API（需要管理员JWT认证）:")
            print("   GET  /api/v1/admin/stats/overview")
            print("   GET  /api/v1/admin/cards")
            print("   POST /api/v1/admin/cards")
            print("   GET  /api/v1/admin/users")
            print(f"   管理员用户: {settings.ADMIN_USERS}")
            print()

        print("🧪 测试命令:")
        print('   curl -H "Authorization: Bearer <your_jwt_token>" http://localhost:8000/api/v1/quota/info')
        print()
    else:
        print("⚠️  JWT认证系统未启用")
        print("   请设置 ENABLE_QUOTA_SYSTEM=true 启用认证")
        print()


def main():
    """启动服务器"""
    print("🚀 启动 ElevenLabs STT Backend 服务器（JWT认证版本）...")
    print(f"📁 项目目录: {project_root}")
    print()

    # 确保必要的目录存在
    upload_dir = project_root / "uploads"
    logs_dir = project_root / "logs"
    upload_dir.mkdir(exist_ok=True)
    logs_dir.mkdir(exist_ok=True)

    print(f"📂 上传目录: {upload_dir}")
    print(f"📝 日志目录: {logs_dir}")
    print()

    # 打印认证配置信息
    print_auth_info()

    print("📖 API文档:")
    print("   Swagger UI: http://localhost:8000/docs")
    print("   ReDoc:      http://localhost:8000/redoc")
    print()

    print("🌐 服务地址:")
    print("   本地访问:   http://localhost:8000")
    print("   健康检查:   http://localhost:8000/health")
    print()

    print("=" * 60)
    print("🚀 服务器启动中...")
    print("=" * 60)

    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    main()
