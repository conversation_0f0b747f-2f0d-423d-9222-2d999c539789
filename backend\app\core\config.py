import os
from typing import List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置"""
    
    # Redis 配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # 文件上传配置
    MAX_UPLOAD_SIZE: int = int(os.getenv("MAX_UPLOAD_SIZE", "1073741824"))  # 1GB
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "/tmp/uploads")
    CHUNK_DIR: str = os.getenv("CHUNK_DIR", "/tmp/chunks")
    
    # 并发控制
    MAX_CONCURRENT_TASKS: int = 3
    MAX_REQUESTS_PER_MINUTE: int = 30
    
    # ElevenLabs API 配置
    ELEVENLABS_API_URL: str = "https://api.elevenlabs.io/v1/speech-to-text"
    ELEVENLABS_TIMEOUT: int = 1800
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "/app/logs/app.log"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "https://yourdomain.com"]

    # JWT认证系统配置
    WORKER_BASE_URL: str = os.getenv("WORKER_BASE_URL", "https://your-worker.domain.com")  # Worker基础URL
    WORKER_VERIFY_ENDPOINT: str = os.getenv("WORKER_VERIFY_ENDPOINT", "/api/auth/verify")  # 验证端点
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./quota.db")  # 数据库URL
    ENABLE_QUOTA_SYSTEM: bool = os.getenv("ENABLE_QUOTA_SYSTEM", "true").lower() == "true"  # 配额系统开关

    # 管理员API配置
    ENABLE_ADMIN_API: bool = os.getenv("ENABLE_ADMIN_API", "false").lower() == "true"  # 管理员API开关
    ADMIN_USERS: str = os.getenv("ADMIN_USERS", "")  # 管理员用户列表，逗号分隔

    # 应用配置
    APP_NAME: str = os.getenv("APP_NAME", "ElevenLabs STT Backend")
    APP_VERSION: str = os.getenv("APP_VERSION", "1.0.0")
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.CHUNK_DIR, exist_ok=True)
os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)
