# -*- coding: utf-8 -*-
"""
管理员API - 日志查询
"""

import logging
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from app.auth.admin_dependencies import verify_admin_jwt
from app.core.database import get_db_session
from app.services.admin_service import AdminService
from app.models.admin_models import UsageLogListRequest, AdminResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/logs", tags=["管理员-日志查询"])

@router.get("", response_model=AdminResponse, summary="查看使用日志")
async def list_usage_logs(
    user_id: Optional[str] = Query(None, description="用户ID过滤"),
    limit: int = Query(100, ge=1, le=1000, description="数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    查看使用日志
    
    - **user_id**: 用户ID过滤，为空则查看所有用户
    - **limit**: 数量限制，最大1000
    - **offset**: 偏移量
    - **start_date**: 开始时间过滤
    - **end_date**: 结束时间过滤
    """
    try:
        request = UsageLogListRequest(
            user_id=user_id,
            limit=limit,
            offset=offset,
            start_date=start_date,
            end_date=end_date
        )
        
        admin_service = AdminService(db_session)
        result = await admin_service.list_usage_logs(request)
        
        logger.info(f"管理员 {admin_user} 查看使用日志: 用户={user_id or '全部'}, 数量={len(result['logs'])}")
        
        return AdminResponse(
            success=True,
            data=result,
            message="日志查询成功"
        )
        
    except Exception as e:
        logger.error(f"查看使用日志失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "查询失败",
                "message": "查看使用日志时发生错误",
                "hint": "请稍后重试或联系管理员"
            }
        )

@router.get("/export", response_model=AdminResponse, summary="导出使用日志")
async def export_usage_logs(
    user_id: Optional[str] = Query(None, description="用户ID过滤"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    format: str = Query("json", regex="^(json|csv)$", description="导出格式"),
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    导出使用日志
    
    - **user_id**: 用户ID过滤
    - **start_date**: 开始时间
    - **end_date**: 结束时间
    - **format**: 导出格式 (json/csv)
    
    注意：导出功能当前返回JSON格式数据，CSV格式需要前端处理
    """
    try:
        # 导出时不限制数量，但设置合理上限
        request = UsageLogListRequest(
            user_id=user_id,
            limit=10000,  # 导出上限
            offset=0,
            start_date=start_date,
            end_date=end_date
        )
        
        admin_service = AdminService(db_session)
        result = await admin_service.list_usage_logs(request)
        
        export_data = {
            "export_info": {
                "exported_by": admin_user,
                "export_time": datetime.utcnow().isoformat(),
                "filter_user_id": user_id,
                "filter_start_date": start_date.isoformat() if start_date else None,
                "filter_end_date": end_date.isoformat() if end_date else None,
                "total_records": len(result['logs']),
                "format": format
            },
            "logs": result['logs']
        }
        
        logger.info(f"管理员 {admin_user} 导出使用日志: 用户={user_id or '全部'}, "
                   f"格式={format}, 记录数={len(result['logs'])}")
        
        return AdminResponse(
            success=True,
            data=export_data,
            message=f"日志导出成功，共{len(result['logs'])}条记录"
        )
        
    except Exception as e:
        logger.error(f"导出使用日志失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "导出失败",
                "message": "导出使用日志时发生错误",
                "hint": "请稍后重试或联系管理员"
            }
        )

@router.get("/summary", response_model=AdminResponse, summary="日志统计摘要")
async def get_logs_summary(
    user_id: Optional[str] = Query(None, description="用户ID过滤"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    获取日志统计摘要
    
    - **user_id**: 用户ID过滤
    - **start_date**: 开始时间
    - **end_date**: 结束时间
    """
    try:
        # 获取日志数据
        request = UsageLogListRequest(
            user_id=user_id,
            limit=10000,  # 统计时获取更多数据
            offset=0,
            start_date=start_date,
            end_date=end_date
        )
        
        admin_service = AdminService(db_session)
        result = await admin_service.list_usage_logs(request)
        
        # 计算统计信息
        logs = result['logs']
        total_records = len(logs)
        total_quota_used = sum(log['quota_used'] for log in logs)
        total_quota_used_hours = total_quota_used / 3600
        
        # 按用户统计
        user_stats = {}
        for log in logs:
            uid = log['user_id']
            if uid not in user_stats:
                user_stats[uid] = {'count': 0, 'quota_used': 0}
            user_stats[uid]['count'] += 1
            user_stats[uid]['quota_used'] += log['quota_used']
        
        # 排序用户统计
        top_users = sorted(
            [{'user_id': uid, **stats, 'quota_used_hours': stats['quota_used'] / 3600} 
             for uid, stats in user_stats.items()],
            key=lambda x: x['quota_used'],
            reverse=True
        )[:10]  # 前10名用户
        
        summary = {
            "filter_info": {
                "user_id": user_id,
                "start_date": start_date.isoformat() if start_date else None,
                "end_date": end_date.isoformat() if end_date else None
            },
            "overall": {
                "total_records": total_records,
                "total_quota_used_seconds": total_quota_used,
                "total_quota_used_hours": round(total_quota_used_hours, 2),
                "unique_users": len(user_stats),
                "average_usage_per_record": round(total_quota_used / total_records, 2) if total_records > 0 else 0
            },
            "top_users": top_users
        }
        
        logger.info(f"管理员 {admin_user} 查看日志统计摘要: 用户={user_id or '全部'}, "
                   f"记录数={total_records}, 总使用={total_quota_used_hours:.2f}小时")
        
        return AdminResponse(
            success=True,
            data=summary,
            message="日志统计摘要获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取日志统计摘要失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "统计失败",
                "message": "获取日志统计摘要时发生错误"
            }
        )
