from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.api.transcribe import router as transcribe_router
from app.api.auth import router as auth_router
from app.api.quota import router as quota_router
from app.core.dependencies import get_task_service, get_file_service
from app.core.database import init_database

# 管理员API路由
from app.api.admin.cards import router as admin_cards_router
from app.api.admin.users import router as admin_users_router
from app.api.admin.stats import router as admin_stats_router
from app.api.admin.logs import router as admin_logs_router

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(settings.LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="ElevenLabs Speech-to-Text API 后端服务",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(transcribe_router)
app.include_router(auth_router)
app.include_router(quota_router)

# 注册管理员API路由（条件性注册）
if settings.ENABLE_ADMIN_API:
    app.include_router(admin_cards_router, prefix="/api/v1/admin")
    app.include_router(admin_users_router, prefix="/api/v1/admin")
    app.include_router(admin_stats_router, prefix="/api/v1/admin")
    app.include_router(admin_logs_router, prefix="/api/v1/admin")
    logger.info("管理员API已启用")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查Redis连接
        task_service = get_task_service()
        await task_service.redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "services": {
                "redis": "connected",
                "api": "running"
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "内部服务器错误",
            "message": str(exc) if settings.DEBUG else "服务暂时不可用"
        }
    )

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info(f"{settings.APP_NAME} v{settings.APP_VERSION} 启动中...")
    logger.info(f"Redis URL: {settings.REDIS_URL}")
    logger.info(f"上传目录: {settings.UPLOAD_DIR}")
    logger.info(f"最大并发任务: {settings.MAX_CONCURRENT_TASKS}")

    # 初始化数据库（如果启用配额系统）
    if settings.ENABLE_QUOTA_SYSTEM:
        try:
            await init_database()
            logger.info("配额系统数据库初始化成功")
        except Exception as e:
            logger.error(f"配额系统数据库初始化失败: {e}")
    else:
        logger.info("配额系统未启用")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info(f"{settings.APP_NAME} 正在关闭...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
