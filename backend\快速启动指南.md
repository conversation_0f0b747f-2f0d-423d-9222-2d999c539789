# 🚀 ElevenLabs STT Backend 快速启动指南

## 📋 目录
- [系统要求](#系统要求)
- [5分钟快速启动](#5分钟快速启动)
- [详细启动步骤](#详细启动步骤)
- [启动验证](#启动验证)
- [常见问题](#常见问题)
- [不同环境启动](#不同环境启动)

## 🔧 系统要求

### 必需软件
- **Python**: 3.11+ (推荐 3.11)
- **Docker**: 20.10+ 和 Docker Compose 2.0+
- **Git**: 用于克隆项目

### 可选软件（本地开发）
- **Redis**: 7.0+ (Docker方式会自动安装)
- **FFmpeg**: 音频处理 (Docker方式会自动安装)

### 系统兼容性
- ✅ **Windows**: 完全支持
- ✅ **macOS**: 完全支持  
- ✅ **Linux**: 完全支持

## ⚡ 5分钟快速启动

### 方式一：Docker启动（推荐）

```bash
# 1. 克隆项目
git clone <your-repository-url>
cd speech-to-text/backend

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，至少修改以下配置：
# API_KEY=your_secure_api_key_here
# WORKER_BASE_URL=https://your-worker.domain.com

# 3. 启动所有服务
docker-compose up -d

# 4. 验证启动
curl http://localhost:8000/health
```

### 方式二：本地开发启动

```bash
# 1. 克隆项目
git clone <your-repository-url>
cd speech-to-text/backend

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 4. 启动Redis（需要单独安装）
# Windows: 下载并启动Redis
# macOS: brew install redis && redis-server
# Linux: sudo systemctl start redis

# 5. 启动应用
python start_server.py
```

## 📖 详细启动步骤

### 步骤1: 环境准备

#### 1.1 检查Python版本
```bash
python --version
# 应该显示 Python 3.11.x 或更高版本
```

#### 1.2 检查Docker（如果使用Docker）
```bash
docker --version
docker-compose --version
```

### 步骤2: 项目配置

#### 2.1 克隆项目
```bash
git clone <your-repository-url>
cd speech-to-text/backend
```

#### 2.2 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env
```

**重要配置项说明**：

```env
# 🔑 必须修改的配置
API_KEY=your_secure_api_key_here                    # 你的API密钥
WORKER_BASE_URL=https://your-worker.domain.com      # Worker服务地址

# 🔧 可选配置（有默认值）
REDIS_URL=redis://localhost:6379/0                  # Redis连接地址
ENABLE_AUTH=true                                     # 是否启用API认证
ENABLE_QUOTA_SYSTEM=true                            # 是否启用配额系统
ENABLE_ADMIN_API=true                               # 是否启用管理员API

# 👥 管理员配置（如果启用管理员API）
ADMIN_USERS=admin_user,super_admin,manager_001      # 管理员用户列表

# 📊 性能配置
MAX_CONCURRENT_TASKS=3                              # 最大并发任务数
MAX_REQUESTS_PER_MINUTE=30                          # 每分钟最大请求数
MAX_UPLOAD_SIZE=1073741824                          # 最大上传文件大小(1GB)

# 📝 日志配置
LOG_LEVEL=INFO                                       # 日志级别
DEBUG=false                                          # 调试模式
```

### 步骤3: 选择启动方式

#### 方式A: Docker启动（生产推荐）

**优势**：
- ✅ 环境隔离，无依赖冲突
- ✅ 自动安装Redis、FFmpeg等依赖
- ✅ 包含Nginx反向代理
- ✅ 自动重启和健康检查

**启动命令**：
```bash
# 启动所有服务（后台运行）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f api

# 停止服务
docker-compose down
```

#### 方式B: 本地开发启动

**优势**：
- ✅ 代码热重载，开发便利
- ✅ 直接调试，无容器隔离
- ✅ 资源占用更少

**启动步骤**：
```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 启动Redis（必需）
# Windows: 下载Redis并启动
# macOS: brew install redis && redis-server
# Linux: sudo systemctl start redis

# 3. 初始化数据库（如果启用配额系统）
python scripts/manage_quota.py init_db

# 4. 启动应用（选择一种方式）
python start_server.py              # 基础启动
python start_with_auth.py           # 带认证信息显示
python -m app.main                  # 直接启动
uvicorn app.main:app --reload       # 开发模式
```

#### 方式C: 专用启动脚本

**基础启动脚本**：
```bash
python start_server.py
```
- 自动创建必要目录
- 显示服务地址和文档链接
- 开发模式，支持热重载

**认证启动脚本**：
```bash
python start_with_auth.py
```
- 显示认证配置信息
- 显示API密钥和认证方式
- 显示测试命令

## ✅ 启动验证

### 1. 基础健康检查
```bash
# 检查服务是否启动
curl http://localhost:8000/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": {
    "redis": "connected",
    "api": "running"
  }
}
```

### 2. API文档访问
```bash
# 浏览器访问
http://localhost:8000/docs      # Swagger UI
http://localhost:8000/redoc     # ReDoc
http://localhost:8000/          # 主页
```

### 3. JWT认证系统检查
```bash
# 获取认证信息（无需认证）
curl http://localhost:8000/api/v1/auth/info

# 获取配额信息（需要JWT认证）
curl -H "Authorization: Bearer your_jwt_token" \
     http://localhost:8000/api/v1/quota/info
```

### 4. 配额系统检查（如果启用）
```bash
# 检查配额信息（需要JWT认证）
curl -H "Authorization: Bearer your_jwt_token" \
     http://localhost:8000/api/v1/quota/info
```

### 5. 管理员API检查（如果启用）
```bash
# 检查管理员信息（需要管理员JWT认证）
curl -H "Authorization: Bearer admin_jwt_token" \
     http://localhost:8000/api/v1/admin/stats/admin-info
```

### 6. 日志检查

**Docker方式**：
```bash
# 查看API服务日志
docker-compose logs api

# 查看Redis日志
docker-compose logs redis

# 实时查看日志
docker-compose logs -f api
```

**本地方式**：
```bash
# 查看日志文件
tail -f logs/app.log

# 或在启动终端查看实时日志
```

**成功启动的日志示例**：
```
2024-01-15 10:30:00 - app.main - INFO - ElevenLabs STT Backend v1.0.0 启动中...
2024-01-15 10:30:00 - app.main - INFO - Redis URL: redis://localhost:6379/0
2024-01-15 10:30:00 - app.main - INFO - 上传目录: /tmp/uploads
2024-01-15 10:30:00 - app.main - INFO - 最大并发任务: 3
2024-01-15 10:30:00 - app.main - INFO - 配额系统数据库初始化成功
2024-01-15 10:30:00 - app.main - INFO - 管理员API已启用
2024-01-15 10:30:00 - uvicorn.error - INFO - Started server process
2024-01-15 10:30:00 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:8000
```

## 🔧 常见问题

### 问题1: Redis连接失败

**错误现象**：
```
redis.exceptions.ConnectionError: Error connecting to Redis
```

**解决方案**：

**Docker方式**：
```bash
# 检查Redis容器状态
docker-compose ps redis

# 重启Redis服务
docker-compose restart redis

# 查看Redis日志
docker-compose logs redis
```

**本地方式**：
```bash
# Windows: 检查Redis是否启动
# 下载Redis for Windows并启动redis-server.exe

# macOS: 安装并启动Redis
brew install redis
brew services start redis

# Linux: 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis

# 测试Redis连接
redis-cli ping
# 应该返回: PONG
```

### 问题2: 端口被占用

**错误现象**：
```
OSError: [Errno 98] Address already in use
```

**解决方案**：
```bash
# 查看端口占用情况
# Windows:
netstat -ano | findstr :8000

# macOS/Linux:
lsof -i :8000

# 杀死占用进程
# Windows:
taskkill /PID <进程ID> /F

# macOS/Linux:
kill -9 <进程ID>

# 或者修改端口
# 在 .env 文件中添加：
PORT=8001
# 然后使用: uvicorn app.main:app --port 8001
```

### 问题3: 依赖安装失败

**错误现象**：
```
ERROR: Could not install packages due to an EnvironmentError
```

**解决方案**：
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 如果是权限问题，使用用户安装
pip install --user -r requirements.txt

# 清理缓存重新安装
pip cache purge
pip install -r requirements.txt
```

### 问题4: 数据库初始化失败

**错误现象**：
```
配额系统数据库初始化失败: no such table: user_quota
```

**解决方案**：
```bash
# 手动初始化数据库
python scripts/manage_quota.py init_db

# 检查数据库文件权限
ls -la quota.db

# 删除数据库文件重新初始化
rm quota.db
python scripts/manage_quota.py init_db
```

### 问题5: 环境变量未生效

**错误现象**：
配置修改后不生效

**解决方案**：
```bash
# 确认.env文件位置正确（在backend目录下）
ls -la .env

# 检查.env文件格式（不要有空格）
# 错误: API_KEY = your_key
# 正确: API_KEY=your_key

# 重启服务使配置生效
# Docker方式:
docker-compose down && docker-compose up -d

# 本地方式:
# Ctrl+C 停止服务，然后重新启动
```

### 问题6: Docker构建失败

**错误现象**：
```
ERROR: failed to solve: process "/bin/sh -c apt-get update" did not complete successfully
```

**解决方案**：
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建镜像
docker-compose build --no-cache

# 检查网络连接
docker run --rm alpine ping -c 3 google.com

# 使用国内镜像源（修改Dockerfile）
# 在Dockerfile开头添加：
# RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list
```

### 问题7: 文件上传失败

**错误现象**：
```
413 Request Entity Too Large
```

**解决方案**：
```bash
# 检查上传大小限制配置
# .env文件中：
MAX_UPLOAD_SIZE=1073741824  # 1GB

# Docker方式还需要检查nginx配置
# nginx.conf中：
client_max_body_size 1G;

# 重启服务使配置生效
docker-compose restart
```

## 🌍 不同环境启动

### 开发环境

**特点**：代码热重载、详细日志、调试模式

```bash
# 启用调试模式
echo "DEBUG=true" >> .env
echo "LOG_LEVEL=DEBUG" >> .env

# 使用开发启动脚本
python start_server.py

# 或直接使用uvicorn
uvicorn app.main:app --reload --log-level debug
```

### 测试环境

**特点**：模拟生产环境、测试数据隔离

```bash
# 使用测试配置
cp .env.example .env.test
# 修改测试配置...

# 使用测试环境变量启动
export ENV_FILE=.env.test
python start_server.py

# 运行测试
python test_basic.py
python test_auth.py
python test_quota_system.py
```

### 生产环境

**特点**：性能优化、安全加固、监控完善

```bash
# 生产环境配置
echo "DEBUG=false" >> .env
echo "LOG_LEVEL=INFO" >> .env
echo "WORKERS=4" >> .env

# 使用Docker生产部署
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 或使用生产级WSGI服务器
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 📊 性能优化

### 1. 并发配置
```env
# 根据服务器配置调整
MAX_CONCURRENT_TASKS=8        # CPU核心数
MAX_REQUESTS_PER_MINUTE=100   # 根据需求调整
```

### 2. 内存优化
```env
# 限制上传文件大小
MAX_UPLOAD_SIZE=536870912     # 512MB

# 配置Redis内存限制
# redis.conf: maxmemory 1gb
```

### 3. 日志优化
```env
# 生产环境减少日志级别
LOG_LEVEL=WARNING

# 配置日志轮转
# 使用logrotate或Docker日志驱动
```

## 🔒 安全配置

### 1. API密钥安全
```bash
# 生成强密钥
openssl rand -hex 32

# 定期轮换密钥
# 更新.env文件中的API_KEY
```

### 2. 网络安全
```bash
# 限制访问IP（nginx配置）
# allow ***********/24;
# deny all;

# 启用HTTPS（生产环境）
# 配置SSL证书
```

### 3. 容器安全
```bash
# 使用非root用户运行
# Dockerfile中添加：
# RUN adduser --disabled-password --gecos '' appuser
# USER appuser
```

## 📞 获取帮助

### 1. 日志分析
```bash
# 查看详细错误日志
docker-compose logs api | grep ERROR

# 查看启动日志
docker-compose logs api | head -20
```

### 2. 健康检查
```bash
# 完整健康检查脚本
curl -s http://localhost:8000/health | jq .
```

### 3. 配置验证
```bash
# 验证配置文件
python -c "from app.core.config import settings; print(settings.dict())"
```

## 🧪 快速功能测试

### 1. 基础API测试
```bash
# 测试根路径
curl http://localhost:8000/

# 预期响应
{
  "message": "欢迎使用 ElevenLabs STT Backend",
  "version": "1.0.0",
  "docs": "/docs"
}
```

### 2. 认证系统测试
```bash
# 获取认证信息
curl http://localhost:8000/api/v1/auth/info

# 验证API密钥（替换为你的API密钥）
curl -H "Authorization: Bearer your_api_key" \
     http://localhost:8000/api/v1/auth/verify

# 预期响应
{
  "message": "API密钥验证成功",
  "authenticated": true,
  "authenticated_at": "2024-01-15T10:30:00Z"
}
```

### 3. 配额系统测试（如果启用）
```bash
# 获取配额信息（需要JWT token）
curl -H "Authorization: Bearer your_jwt_token" \
     http://localhost:8000/api/v1/quota/info

# 预期响应
{
  "success": true,
  "data": {
    "user_id": "555",
    "quota_balance": 36000,
    "quota_balance_hours": 10.0,
    "total_quota": 36000,
    "total_quota_hours": 10.0
  }
}
```

### 4. 管理员API测试（如果启用）
```bash
# 获取管理员信息（需要管理员JWT token）
curl -H "Authorization: Bearer admin_jwt_token" \
     http://localhost:8000/api/v1/admin/stats/admin-info

# 预期响应
{
  "success": true,
  "data": {
    "current_admin": "admin_user",
    "admin_users": ["admin_user", "super_admin"],
    "admin_count": 2
  }
}
```

### 5. 运行测试脚本
```bash
# 基础功能测试
python test_basic.py

# JWT认证系统测试
python test_quota_system.py

# API集成测试
python test_api_integration.py
```

## 📚 相关文档

- **[README.md](./README.md)** - 项目概述和基础使用
- **[JWT认证使用说明.md](./JWT认证使用说明.md)** - JWT认证详细说明
- **[配额认证系统部署指南.md](./配额认证系统部署指南.md)** - 配额系统部署和配置
- **[管理员API接口文档.md](./管理员API接口文档.md)** - 管理员API完整文档
- **[前端JWT认证适配指南.md](./前端JWT认证适配指南.md)** - 前端集成指导

## 🎯 下一步

启动成功后，你可以：

1. **浏览API文档**: http://localhost:8000/docs
2. **测试音频转录功能**: 上传音频文件进行转录
3. **配置管理员权限**: 设置管理员用户进行系统管理
4. **集成前端应用**: 使用JWT认证集成前端
5. **监控系统状态**: 查看日志和健康检查

如果遇到其他问题，请：
1. 检查日志文件获取详细错误信息
2. 确认环境变量配置正确
3. 验证所有依赖服务正常运行
4. 参考项目文档或联系技术支持

---

**🎉 恭喜！你已经成功启动了ElevenLabs STT Backend服务！**
