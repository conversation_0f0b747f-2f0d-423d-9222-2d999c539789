events {
    worker_connections 1024;
}

http {
    upstream api_backend {
        server api:8000;
    }

    # 文件上传大小限制
    client_max_body_size 1G;

    # 超时设置
    proxy_connect_timeout 60s;
    proxy_send_timeout 1800s;
    proxy_read_timeout 1800s;

    server {
        listen 80;
        server_name localhost;

        # API 代理
        location /api/ {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 静态文件服务 (前端)
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

        # 健康检查
        location /health {
            proxy_pass http://api_backend/health;
        }
    }
}
