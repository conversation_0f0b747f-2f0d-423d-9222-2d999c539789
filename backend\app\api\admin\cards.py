# -*- coding: utf-8 -*-
"""
管理员API - 卡密管理
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from app.auth.admin_dependencies import verify_admin_jwt
from app.core.database import get_db_session
from app.services.admin_service import AdminService
from app.models.admin_models import (
    CreateCardRequest, BatchCreateCardRequest, CardListRequest,
    CardListResponse, AdminResponse, CardStatus
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cards", tags=["管理员-卡密管理"])

@router.get("", response_model=CardListResponse, summary="查看卡密列表")
async def list_cards(
    status: CardStatus = Query(CardStatus.ALL, description="卡密状态"),
    limit: int = Query(50, ge=1, le=200, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    search: Optional[str] = Query(None, max_length=100, description="搜索关键词"),
    quota_min: Optional[float] = Query(None, ge=0, description="最小配额过滤(小时)"),
    quota_max: Optional[float] = Query(None, ge=0, description="最大配额过滤(小时)"),
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    查看卡密列表
    
    - **status**: 卡密状态 (all/unused/used)
    - **limit**: 每页数量，最大200
    - **offset**: 偏移量
    - **search**: 搜索卡密ID或描述
    - **quota_min/max**: 配额范围过滤
    """
    try:
        request = CardListRequest(
            status=status,
            limit=limit,
            offset=offset,
            search=search,
            quota_min=quota_min,
            quota_max=quota_max
        )
        
        admin_service = AdminService(db_session)
        result = await admin_service.list_cards(request)
        
        logger.info(f"管理员 {admin_user} 查看卡密列表: 状态={status}, 数量={len(result['cards'])}")
        
        return CardListResponse(
            success=True,
            data=result
        )
        
    except Exception as e:
        logger.error(f"查看卡密列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "查询失败",
                "message": "查看卡密列表时发生错误",
                "hint": "请稍后重试或联系管理员"
            }
        )

@router.post("", response_model=AdminResponse, summary="创建充值卡")
async def create_card(
    request: CreateCardRequest,
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    创建单张充值卡
    
    - **quota_hours**: 配额小时数 (0-1000)
    - **description**: 卡密描述
    """
    try:
        admin_service = AdminService(db_session)
        result = await admin_service.create_card(request)
        
        logger.info(f"管理员 {admin_user} 创建充值卡: {result.card_id}, 配额: {request.quota_hours}小时")
        
        return AdminResponse(
            success=True,
            data=result.dict(),
            message=f"充值卡创建成功，配额: {request.quota_hours}小时"
        )
        
    except Exception as e:
        logger.error(f"创建充值卡失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "创建失败",
                "message": "创建充值卡时发生错误",
                "hint": "请检查参数或稍后重试"
            }
        )

@router.post("/batch", response_model=AdminResponse, summary="批量创建充值卡")
async def batch_create_cards(
    request: BatchCreateCardRequest,
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    批量创建充值卡
    
    - **quota_hours**: 配额小时数 (0-1000)
    - **description**: 卡密描述
    - **count**: 创建数量 (1-1000)
    """
    try:
        admin_service = AdminService(db_session)
        result = await admin_service.batch_create_cards(request)
        
        logger.info(f"管理员 {admin_user} 批量创建充值卡: {request.count}张, 配额: {request.quota_hours}小时/张")
        
        return AdminResponse(
            success=True,
            data=result,
            message=f"批量创建成功: {request.count}张充值卡，每张{request.quota_hours}小时"
        )
        
    except Exception as e:
        logger.error(f"批量创建充值卡失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "批量创建失败",
                "message": "批量创建充值卡时发生错误",
                "hint": "请检查参数或稍后重试"
            }
        )

@router.get("/{card_id}", response_model=AdminResponse, summary="查看单个卡密详情")
async def get_card_detail(
    card_id: str,
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    查看单个卡密详情
    
    - **card_id**: 卡密ID
    """
    try:
        # 这里可以实现单个卡密查询逻辑
        # 为了简化，暂时返回基本信息
        return AdminResponse(
            success=True,
            data={"card_id": card_id, "message": "单个卡密查询功能待实现"},
            message="查询成功"
        )
        
    except Exception as e:
        logger.error(f"查看卡密详情失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "查询失败",
                "message": "查看卡密详情时发生错误"
            }
        )
