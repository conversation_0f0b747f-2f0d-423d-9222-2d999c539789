#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配额系统测试脚本
"""

import asyncio
import aiohttp
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scripts.manage_quota import create_recharge_card

BASE_URL = "http://localhost:8000"

async def test_quota_system():
    """测试配额系统"""
    print("🧪 开始测试配额系统...")
    
    # 1. 创建测试充值卡
    print("\n1. 创建测试充值卡...")
    card_id = await create_recharge_card(10, "测试10小时套餐")
    
    # 2. 测试JWT认证（模拟）
    print("\n2. 测试JWT认证...")
    test_jwt = "test_jwt_token_for_user_123"
    
    async with aiohttp.ClientSession() as session:
        # 3. 测试获取配额信息（无配额状态）
        print("\n3. 测试获取配额信息（无配额状态）...")
        try:
            async with session.get(
                f"{BASE_URL}/api/v1/quota/info",
                headers={"Authorization": f"Bearer {test_jwt}"}
            ) as response:
                if response.status == 401:
                    print("✅ JWT认证失败测试通过（预期行为）")
                else:
                    data = await response.json()
                    print(f"📊 配额信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 获取配额信息失败: {e}")
        
        # 4. 测试充值功能
        print(f"\n4. 测试充值功能（卡密: {card_id}）...")
        try:
            async with session.post(
                f"{BASE_URL}/api/v1/quota/recharge",
                headers={"Authorization": f"Bearer {test_jwt}"},
                json={"card_id": card_id}
            ) as response:
                if response.status == 401:
                    print("✅ JWT认证失败测试通过（预期行为）")
                else:
                    data = await response.json()
                    print(f"💳 充值结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ 充值测试失败: {e}")
        
        # 5. 测试JWT上传接口
        print("\n5. 测试JWT上传接口...")
        try:
            # 创建测试音频文件
            test_audio_content = b"fake audio content for testing"
            
            form_data = aiohttp.FormData()
            form_data.add_field('file', test_audio_content, filename='test.mp3', content_type='audio/mpeg')
            form_data.add_field('language_code', 'zh')
            
            async with session.post(
                f"{BASE_URL}/api/v1/transcribe/upload-jwt",
                headers={"Authorization": f"Bearer {test_jwt}"},
                data=form_data
            ) as response:
                if response.status == 401:
                    print("✅ JWT认证失败测试通过（预期行为）")
                elif response.status == 402:
                    print("✅ 配额不足测试通过（预期行为）")
                else:
                    data = await response.json()
                    print(f"🎵 上传结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"❌ JWT上传测试失败: {e}")

async def test_api_endpoints():
    """测试API端点可用性"""
    print("\n🔍 测试API端点可用性...")
    
    endpoints = [
        "/api/v1/quota/info",
        "/api/v1/quota/recharge", 
        "/api/v1/transcribe/upload-jwt",
        "/health"
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint in endpoints:
            try:
                async with session.get(f"{BASE_URL}{endpoint}") as response:
                    if endpoint == "/health":
                        print(f"✅ {endpoint}: {response.status}")
                    else:
                        # 对于需要认证的端点，401是预期的
                        if response.status in [401, 422]:  # 401未认证, 422验证错误
                            print(f"✅ {endpoint}: {response.status} (预期)")
                        else:
                            print(f"⚠️  {endpoint}: {response.status}")
            except Exception as e:
                print(f"❌ {endpoint}: 连接失败 - {e}")

async def main():
    """主函数"""
    print("🚀 配额系统集成测试")
    print("=" * 50)
    
    try:
        # 测试API端点
        await test_api_endpoints()
        
        # 测试配额系统
        await test_quota_system()
        
        print("\n" + "=" * 50)
        print("✅ 测试完成")
        print("\n📝 测试说明:")
        print("- JWT认证失败是预期行为（需要真实的Worker验证）")
        print("- 配额不足是预期行为（新用户无配额）")
        print("- 可以使用manage_quota.py脚本管理配额和充值卡")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
