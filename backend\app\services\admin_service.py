# -*- coding: utf-8 -*-
"""
管理员服务
"""

import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc

from app.models.quota_models import RechargeCard, UserQuota, QuotaUsageLog
from app.models.admin_models import (
    CardListRequest, CreateCardRequest, BatchCreateCardRequest,
    UserListRequest, ModifyUserQuotaRequest, UsageLogListRequest,
    CardInfo, UserInfo, SystemStats, UsageLogInfo
)

logger = logging.getLogger(__name__)

class AdminService:
    """管理员服务类"""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    # ========================================================================
    # 卡密管理
    # ========================================================================
    
    async def create_card(self, request: CreateCardRequest) -> CardInfo:
        """创建单张充值卡"""
        card_id = str(uuid.uuid4())
        quota_amount = int(request.quota_hours * 3600)  # 转换为秒
        
        card = RechargeCard(
            card_id=card_id,
            quota_amount=quota_amount,
            description=request.description,
            is_used=False,
            created_at=datetime.utcnow()
        )
        
        self.db_session.add(card)
        await self.db_session.commit()
        await self.db_session.refresh(card)
        
        logger.info(f"管理员创建充值卡: {card_id}, 配额: {request.quota_hours}小时")
        
        return CardInfo(
            card_id=card.card_id,
            quota_amount=card.quota_amount,
            quota_hours=card.quota_amount / 3600,
            description=card.description,
            is_used=card.is_used,
            used_by=card.used_by,
            used_at=card.used_at,
            created_at=card.created_at
        )
    
    async def batch_create_cards(self, request: BatchCreateCardRequest) -> Dict[str, Any]:
        """批量创建充值卡"""
        quota_amount = int(request.quota_hours * 3600)
        created_cards = []
        card_ids = []
        
        for i in range(request.count):
            card_id = str(uuid.uuid4())
            card = RechargeCard(
                card_id=card_id,
                quota_amount=quota_amount,
                description=f"{request.description} #{i+1}" if request.description else f"批量卡密 #{i+1}",
                is_used=False,
                created_at=datetime.utcnow()
            )
            created_cards.append(card)
            card_ids.append(card_id)
        
        self.db_session.add_all(created_cards)
        await self.db_session.commit()
        
        logger.info(f"管理员批量创建充值卡: {request.count}张, 配额: {request.quota_hours}小时/张")
        
        return {
            "created_count": request.count,
            "quota_hours": request.quota_hours,
            "total_quota_seconds": quota_amount * request.count,
            "description": request.description,
            "card_ids": card_ids[:10]  # 只返回前10个ID，避免响应过大
        }
    
    async def list_cards(self, request: CardListRequest) -> Dict[str, Any]:
        """查看卡密列表"""
        # 构建查询
        query = select(RechargeCard)
        
        # 状态过滤
        if request.status == "unused":
            query = query.where(RechargeCard.is_used == False)
        elif request.status == "used":
            query = query.where(RechargeCard.is_used == True)
        
        # 搜索过滤
        if request.search:
            search_pattern = f"%{request.search}%"
            query = query.where(
                or_(
                    RechargeCard.card_id.like(search_pattern),
                    RechargeCard.description.like(search_pattern)
                )
            )
        
        # 时间过滤
        if request.created_after:
            query = query.where(RechargeCard.created_at >= request.created_after)
        
        # 配额过滤
        if request.quota_min is not None:
            query = query.where(RechargeCard.quota_amount >= int(request.quota_min * 3600))
        if request.quota_max is not None:
            query = query.where(RechargeCard.quota_amount <= int(request.quota_max * 3600))
        
        # 总数查询
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.order_by(desc(RechargeCard.created_at))
        query = query.offset(request.offset).limit(request.limit)
        
        result = await self.db_session.execute(query)
        cards = result.scalars().all()
        
        # 转换为响应格式
        card_list = []
        for card in cards:
            card_list.append(CardInfo(
                card_id=card.card_id,
                quota_amount=card.quota_amount,
                quota_hours=card.quota_amount / 3600,
                description=card.description,
                is_used=card.is_used,
                used_by=card.used_by,
                used_at=card.used_at,
                created_at=card.created_at
            ))
        
        # 统计信息
        summary = await self._get_card_summary()
        
        return {
            "cards": [card.dict() for card in card_list],
            "pagination": {
                "total": total,
                "limit": request.limit,
                "offset": request.offset,
                "has_next": request.offset + request.limit < total
            },
            "summary": summary
        }
    
    async def _get_card_summary(self) -> Dict[str, Any]:
        """获取卡密统计信息"""
        # 总卡密数
        total_query = select(func.count()).select_from(RechargeCard)
        total_result = await self.db_session.execute(total_query)
        total_cards = total_result.scalar()
        
        # 未使用卡密数
        unused_query = select(func.count()).select_from(RechargeCard).where(RechargeCard.is_used == False)
        unused_result = await self.db_session.execute(unused_query)
        unused_cards = unused_result.scalar()
        
        # 已使用卡密数
        used_cards = total_cards - unused_cards
        
        # 总配额
        total_quota_query = select(func.sum(RechargeCard.quota_amount)).select_from(RechargeCard)
        total_quota_result = await self.db_session.execute(total_quota_query)
        total_quota_seconds = total_quota_result.scalar() or 0
        
        # 已使用配额
        used_quota_query = select(func.sum(RechargeCard.quota_amount)).select_from(RechargeCard).where(RechargeCard.is_used == True)
        used_quota_result = await self.db_session.execute(used_quota_query)
        used_quota_seconds = used_quota_result.scalar() or 0
        
        return {
            "total_cards": total_cards,
            "unused_cards": unused_cards,
            "used_cards": used_cards,
            "total_quota_seconds": total_quota_seconds,
            "used_quota_seconds": used_quota_seconds
        }
    
    # ========================================================================
    # 用户管理
    # ========================================================================
    
    async def list_users(self, request: UserListRequest) -> Dict[str, Any]:
        """查看用户列表"""
        # 构建查询
        query = select(UserQuota)
        
        # 搜索过滤
        if request.search:
            search_pattern = f"%{request.search}%"
            query = query.where(UserQuota.user_id.like(search_pattern))
        
        # 状态过滤
        if request.status == "active":
            query = query.where(UserQuota.quota_balance > 0)
        elif request.status == "exhausted":
            query = query.where(UserQuota.quota_balance <= 0)
        
        # 总数查询
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.order_by(desc(UserQuota.last_updated))
        query = query.offset(request.offset).limit(request.limit)
        
        result = await self.db_session.execute(query)
        users = result.scalars().all()
        
        # 获取用户使用统计
        user_list = []
        for user in users:
            # 获取用户使用统计
            usage_stats = await self._get_user_usage_stats(user.user_id)
            
            user_list.append(UserInfo(
                user_id=user.user_id,
                quota_balance=user.quota_balance,
                quota_balance_hours=user.quota_balance / 3600,
                total_quota=user.total_quota,
                total_quota_hours=user.total_quota / 3600,
                expiry_date=user.expiry_date,
                status="active" if user.quota_balance > 0 else "exhausted",
                last_updated=user.last_updated,
                total_usage=usage_stats["total_usage"],
                total_usage_hours=usage_stats["total_usage"] / 3600,
                recharge_count=usage_stats["recharge_count"],
                last_activity=usage_stats["last_activity"]
            ))
        
        # 统计信息
        summary = await self._get_user_summary()
        
        return {
            "users": [user.dict() for user in user_list],
            "pagination": {
                "total": total,
                "limit": request.limit,
                "offset": request.offset,
                "has_next": request.offset + request.limit < total
            },
            "summary": summary
        }
    
    async def _get_user_usage_stats(self, user_id: str) -> Dict[str, Any]:
        """获取用户使用统计"""
        # 总使用量
        usage_query = select(func.sum(QuotaUsageLog.quota_amount)).where(QuotaUsageLog.user_id == user_id)
        usage_result = await self.db_session.execute(usage_query)
        total_usage = usage_result.scalar() or 0
        
        # 充值次数（通过充值卡使用记录计算）
        recharge_query = select(func.count()).select_from(RechargeCard).where(RechargeCard.used_by == user_id)
        recharge_result = await self.db_session.execute(recharge_query)
        recharge_count = recharge_result.scalar() or 0
        
        # 最后活动时间
        last_activity_query = select(func.max(QuotaUsageLog.created_at)).where(QuotaUsageLog.user_id == user_id)
        last_activity_result = await self.db_session.execute(last_activity_query)
        last_activity = last_activity_result.scalar()
        
        return {
            "total_usage": total_usage,
            "recharge_count": recharge_count,
            "last_activity": last_activity
        }
    
    async def _get_user_summary(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        # 总用户数
        total_query = select(func.count()).select_from(UserQuota)
        total_result = await self.db_session.execute(total_query)
        total_users = total_result.scalar()
        
        # 活跃用户数
        active_query = select(func.count()).select_from(UserQuota).where(UserQuota.quota_balance > 0)
        active_result = await self.db_session.execute(active_query)
        active_users = active_result.scalar()
        
        # 配额耗尽用户数
        exhausted_users = total_users - active_users
        
        # 总分发配额
        total_quota_query = select(func.sum(UserQuota.total_quota)).select_from(UserQuota)
        total_quota_result = await self.db_session.execute(total_quota_query)
        total_quota_distributed = total_quota_result.scalar() or 0
        
        # 总使用配额
        total_used_query = select(func.sum(QuotaUsageLog.quota_amount)).select_from(QuotaUsageLog)
        total_used_result = await self.db_session.execute(total_used_query)
        total_quota_used = total_used_result.scalar() or 0
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "exhausted_users": exhausted_users,
            "total_quota_distributed": total_quota_distributed,
            "total_quota_used": total_quota_used
        }

    async def modify_user_quota(self, user_id: str, request: ModifyUserQuotaRequest) -> Dict[str, Any]:
        """修改用户配额"""
        # 查找用户
        query = select(UserQuota).where(UserQuota.user_id == user_id)
        result = await self.db_session.execute(query)
        user_quota = result.scalar_one_or_none()

        if not user_quota:
            # 如果用户不存在，创建新用户
            user_quota = UserQuota(
                user_id=user_id,
                quota_balance=0,
                total_quota=0,
                expiry_date=None,
                last_updated=datetime.utcnow()
            )
            self.db_session.add(user_quota)

        # 计算新配额
        quota_seconds = int(request.quota_hours * 3600)
        old_balance = user_quota.quota_balance
        old_total = user_quota.total_quota

        if request.operation == "add":
            user_quota.quota_balance += quota_seconds
            user_quota.total_quota += quota_seconds
        elif request.operation == "set":
            user_quota.quota_balance = quota_seconds
            user_quota.total_quota = quota_seconds
        elif request.operation == "subtract":
            user_quota.quota_balance = max(0, user_quota.quota_balance - quota_seconds)
            # 减少操作不改变总配额

        user_quota.last_updated = datetime.utcnow()

        await self.db_session.commit()
        await self.db_session.refresh(user_quota)

        logger.info(f"管理员修改用户配额: {user_id}, 操作: {request.operation}, "
                   f"变化: {request.quota_hours}小时, 原因: {request.reason}")

        return {
            "user_id": user_id,
            "operation": request.operation,
            "quota_change_hours": request.quota_hours,
            "old_balance_hours": old_balance / 3600,
            "new_balance_hours": user_quota.quota_balance / 3600,
            "old_total_hours": old_total / 3600,
            "new_total_hours": user_quota.total_quota / 3600,
            "reason": request.reason
        }

    # ========================================================================
    # 使用日志管理
    # ========================================================================

    async def list_usage_logs(self, request: UsageLogListRequest) -> Dict[str, Any]:
        """查看使用日志"""
        # 构建查询
        query = select(QuotaUsageLog)

        # 用户过滤
        if request.user_id:
            query = query.where(QuotaUsageLog.user_id == request.user_id)

        # 时间过滤
        if request.start_date:
            query = query.where(QuotaUsageLog.created_at >= request.start_date)
        if request.end_date:
            query = query.where(QuotaUsageLog.created_at <= request.end_date)

        # 总数查询
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        query = query.order_by(desc(QuotaUsageLog.created_at))
        query = query.offset(request.offset).limit(request.limit)

        result = await self.db_session.execute(query)
        logs = result.scalars().all()

        # 转换为响应格式
        log_list = []
        for log in logs:
            log_list.append(UsageLogInfo(
                log_id=log.id,
                user_id=log.user_id,
                task_id=log.task_id,
                quota_used=log.quota_amount,
                quota_used_hours=log.quota_amount / 3600,
                file_duration=log.audio_duration,
                created_at=log.created_at
            ))

        return {
            "logs": [log.dict() for log in log_list],
            "pagination": {
                "total": total,
                "limit": request.limit,
                "offset": request.offset,
                "has_next": request.offset + request.limit < total
            }
        }

    # ========================================================================
    # 系统统计
    # ========================================================================

    async def get_system_stats(self) -> SystemStats:
        """获取系统统计信息"""
        # 卡密统计
        card_summary = await self._get_card_summary()

        # 用户统计
        user_summary = await self._get_user_summary()

        # 配额统计
        quota_stats = {
            "total_distributed": user_summary["total_quota_distributed"],
            "total_used": user_summary["total_quota_used"],
            "utilization_rate": (
                user_summary["total_quota_used"] / user_summary["total_quota_distributed"]
                if user_summary["total_quota_distributed"] > 0 else 0
            )
        }

        # 活动统计
        activity_stats = await self._get_activity_stats()

        return SystemStats(
            cards={
                "total": card_summary["total_cards"],
                "unused": card_summary["unused_cards"],
                "used": card_summary["used_cards"],
                "total_value_hours": card_summary["total_quota_seconds"] / 3600
            },
            users={
                "total": user_summary["total_users"],
                "active": user_summary["active_users"],
                "exhausted": user_summary["exhausted_users"]
            },
            quota=quota_stats,
            activity=activity_stats
        )

    async def _get_activity_stats(self) -> Dict[str, Any]:
        """获取活动统计"""
        today = datetime.utcnow().date()

        # 今日充值次数
        today_recharge_query = select(func.count()).select_from(RechargeCard).where(
            func.date(RechargeCard.used_at) == today
        )
        today_recharge_result = await self.db_session.execute(today_recharge_query)
        today_recharges = today_recharge_result.scalar() or 0

        # 今日使用时长
        today_usage_query = select(func.sum(QuotaUsageLog.quota_amount)).where(
            func.date(QuotaUsageLog.created_at) == today
        )
        today_usage_result = await self.db_session.execute(today_usage_query)
        today_usage_seconds = today_usage_result.scalar() or 0
        today_usage_hours = today_usage_seconds / 3600

        # 本月新用户（简化实现，基于用户首次充值时间）
        this_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_new_users_query = select(func.count(func.distinct(RechargeCard.used_by))).where(
            and_(
                RechargeCard.used_at >= this_month_start,
                RechargeCard.used_by.isnot(None)
            )
        )
        this_month_new_users_result = await self.db_session.execute(this_month_new_users_query)
        this_month_new_users = this_month_new_users_result.scalar() or 0

        return {
            "today_recharges": today_recharges,
            "today_usage_hours": round(today_usage_hours, 2),
            "this_month_new_users": this_month_new_users
        }
