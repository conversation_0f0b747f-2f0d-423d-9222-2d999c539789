import os
from typing import Optional
from fastapi import HTTPException

# 支持的文件扩展名
ALLOWED_EXTENSIONS = {
    '.mp3', '.wav', '.flac', '.m4a', '.aac', '.ogg',
    '.mp4', '.mov'
}

# 支持的语言代码
SUPPORTED_LANGUAGES = [
    'zh', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'ru', 'it', 'pt'
]

def validate_file_format(filename: Optional[str]) -> None:
    """验证文件格式"""
    if not filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    file_ext = os.path.splitext(filename)[1].lower()
    
    if file_ext not in ALLOWED_EXTENSIONS:
        supported_formats = ', '.join(ALLOWED_EXTENSIONS)
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件格式: {file_ext}。支持的格式: {supported_formats}"
        )

def validate_file_size(file_size: int, max_size: int = 1073741824) -> None:
    """验证文件大小"""
    if file_size > max_size:
        max_size_mb = max_size / 1024 / 1024
        raise HTTPException(
            status_code=400, 
            detail=f"文件大小超过限制。最大允许: {max_size_mb:.0f}MB"
        )

def validate_language_code(language_code: Optional[str]) -> None:
    """验证语言代码"""
    if language_code and language_code not in SUPPORTED_LANGUAGES:
        supported_langs = ', '.join(SUPPORTED_LANGUAGES)
        raise HTTPException(
            status_code=400,
            detail=f"不支持的语言代码: {language_code}。支持的语言: {supported_langs}"
        )

def validate_num_speakers(num_speakers: Optional[int]) -> None:
    """验证说话人数量"""
    if num_speakers is not None:
        if num_speakers < 1 or num_speakers > 32:
            raise HTTPException(
                status_code=400,
                detail="说话人数量必须在1-32之间"
            )

def validate_timestamps_granularity(granularity: str) -> None:
    """验证时间戳粒度"""
    valid_granularities = ['none', 'word', 'character']
    if granularity not in valid_granularities:
        raise HTTPException(
            status_code=400,
            detail=f"无效的时间戳粒度: {granularity}。有效值: {', '.join(valid_granularities)}"
        )
