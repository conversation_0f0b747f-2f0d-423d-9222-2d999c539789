{"language_code": "eng", "language_probability": 0.976867139339447, "text": "He furrowed his brows and said, \"Try to spruce up a bit when you go out next time.\" Unperturbed, I continued to engage with my phone, leaving <PERSON> to awkwardly attempt small talk for the rest of the journey. Upon arriving at the venue, <PERSON> quickly came over to explain why he had to draft <PERSON> in as a groomsman, assuring me that <PERSON> would have brought me along otherwise. He downed three penalty drinks to show his regret. I wasn't at all upset. Don't blame yourself. Happy wedding. While everyone was initially shocked not to see me make a scene as before, I acted as if I was an entirely different person, disinterested in their surprise. Soon, the atmosphere livened up again. Someone wanted to make <PERSON><PERSON> drink, and she looked pitifully at <PERSON>, who promptly stepped in to shield her from the drink. Observing the familiar yet alien Leon, I realized I might never have truly known him. On our third anniversary during dinner, one of his friends insisted I drink despite my protests of feeling unwell. <PERSON>, thinking I was just throwing a temper tantrum, forced me to drink. I ended up in the hospital that night, where I learned I had miscarried.", "words": [{"text": "He", "start": 0.079, "end": 0.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 0.239, "end": 0.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "furrowed", "start": 0.239, "end": 0.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 0.519, "end": 0.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 0.519, "end": 0.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 0.719, "end": 0.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "brows", "start": 0.719, "end": 1.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.059, "end": 1.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 1.059, "end": 1.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.24, "end": 1.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said,", "start": 1.259, "end": 1.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.559, "end": 1.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"Try", "start": 1.559, "end": 1.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.999, "end": 2.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 2.019, "end": 2.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.099, "end": 2.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spruce", "start": 2.099, "end": 2.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.399, "end": 2.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 2.44, "end": 2.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.539, "end": 2.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 2.579, "end": 2.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.599, "end": 2.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bit", "start": 2.659, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "when", "start": 2.799, "end": 2.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.919, "end": 2.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you", "start": 2.939, "end": 3.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.039, "end": 3.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "go", "start": 3.059, "end": 3.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.159, "end": 3.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 3.179, "end": 3.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.339, "end": 3.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "next", "start": 3.359, "end": 3.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.599, "end": 3.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "time.\"", "start": 3.599, "end": 3.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.979, "end": 4.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Unperturbed,", "start": 4.139, "end": 4.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.779, "end": 4.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 4.779, "end": 4.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.899, "end": 4.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "continued", "start": 4.92, "end": 5.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.359, "end": 5.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 5.38, "end": 5.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.46, "end": 5.48, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "engage", "start": 5.48, "end": 5.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.799, "end": 5.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "with", "start": 5.799, "end": 5.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.919, "end": 5.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 5.96, "end": 6.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.119, "end": 6.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "phone,", "start": 6.139, "end": 6.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.479, "end": 6.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "leaving", "start": 6.739, "end": 7.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.0, "end": 7.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 7.039, "end": 7.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.359, "end": 7.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 7.379, "end": 7.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.519, "end": 7.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "awkwardly", "start": 7.599, "end": 8.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.0, "end": 8.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "attempt", "start": 8.039, "end": 8.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.319, "end": 8.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "small", "start": 8.38, "end": 8.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.619, "end": 8.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "talk", "start": 8.679, "end": 8.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.9, "end": 8.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 8.92, "end": 9.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.019, "end": 9.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 9.019, "end": 9.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.179, "end": 9.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "rest", "start": 9.199, "end": 9.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.42, "end": 9.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 9.439, "end": 9.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.519, "end": 9.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 9.539, "end": 9.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.6, "end": 9.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "journey.", "start": 9.659, "end": 10.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.1, "end": 10.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Upon", "start": 10.239, "end": 10.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.539, "end": 10.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "arriving", "start": 10.559, "end": 10.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.939, "end": 10.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 10.96, "end": 11.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.059, "end": 11.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 11.079, "end": 11.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.14, "end": 11.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "venue,", "start": 11.179, "end": 11.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.579, "end": 11.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 11.759, "end": 11.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.979, "end": 11.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "quickly", "start": 11.979, "end": 12.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.379, "end": 12.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "came", "start": 12.399, "end": 12.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.559, "end": 12.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "over", "start": 12.599, "end": 12.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.819, "end": 12.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 12.84, "end": 12.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.939, "end": 12.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "explain", "start": 12.96, "end": 13.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.339, "end": 13.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "why", "start": 13.34, "end": 13.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.46, "end": 13.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 13.479, "end": 13.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.559, "end": 13.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "had", "start": 13.579, "end": 13.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.64, "end": 13.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 13.659, "end": 13.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.739, "end": 13.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "draft", "start": 13.759, "end": 13.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.96, "end": 14.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 14.039, "end": 14.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.42, "end": 14.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 14.46, "end": 14.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.679, "end": 14.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "as", "start": 14.899, "end": 15.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.039, "end": 15.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 15.039, "end": 15.14, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.14, "end": 15.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "groomsman,", "start": 15.159, "end": 15.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.699, "end": 15.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "assuring", "start": 15.939, "end": 16.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.539, "end": 16.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 16.559, "end": 16.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.68, "end": 16.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 16.699, "end": 16.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.8, "end": 16.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 16.819, "end": 17.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.1, "end": 17.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "would", "start": 17.1, "end": 17.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.239, "end": 17.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "have", "start": 17.239, "end": 17.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.379, "end": 17.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "brought", "start": 17.399, "end": 17.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.6, "end": 17.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 17.619, "end": 17.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.699, "end": 17.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "along", "start": 17.779, "end": 18.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.079, "end": 18.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "otherwise.", "start": 18.079, "end": 18.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.76, "end": 18.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 18.92, "end": 19.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.039, "end": 19.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "downed", "start": 19.039, "end": 19.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.379, "end": 19.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "three", "start": 19.379, "end": 19.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.579, "end": 19.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "penalty", "start": 19.639, "end": 20.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.0, "end": 20.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "drinks", "start": 20.02, "end": 20.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.299, "end": 20.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 20.319, "end": 20.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.379, "end": 20.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "show", "start": 20.459, "end": 20.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.579, "end": 20.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 20.619, "end": 20.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.719, "end": 20.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "regret.", "start": 20.739, "end": 21.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.199, "end": 21.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 21.439, "end": 21.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.539, "end": 21.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wasn't", "start": 21.539, "end": 21.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.819, "end": 21.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 21.819, "end": 21.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.919, "end": 21.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 21.92, "end": 22.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.059, "end": 22.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "upset.", "start": 22.1, "end": 22.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.6, "end": 22.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Don't", "start": 22.92, "end": 23.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.159, "end": 23.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "blame", "start": 23.18, "end": 23.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.399, "end": 23.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "yourself.", "start": 23.42, "end": 23.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.94, "end": 24.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Happy", "start": 24.18, "end": 24.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.479, "end": 24.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wedding.", "start": 24.539, "end": 24.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.979, "end": 25.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "While", "start": 25.239, "end": 25.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.379, "end": 25.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "everyone", "start": 25.379, "end": 25.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.679, "end": 25.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 25.68, "end": 25.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.819, "end": 25.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "initially", "start": 25.84, "end": 26.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.199, "end": 26.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shocked", "start": 26.319, "end": 26.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.719, "end": 27.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "not", "start": 27.059, "end": 27.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.239, "end": 27.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 27.239, "end": 27.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.34, "end": 27.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "see", "start": 27.399, "end": 27.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.539, "end": 27.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 27.579, "end": 27.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.679, "end": 27.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "make", "start": 27.699, "end": 27.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.859, "end": 27.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 27.879, "end": 27.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.92, "end": 27.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scene", "start": 27.959, "end": 28.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.139, "end": 28.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "as", "start": 28.139, "end": 28.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.239, "end": 28.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "before,", "start": 28.26, "end": 28.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.759, "end": 29.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 29.099, "end": 29.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.259, "end": 29.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "acted", "start": 29.26, "end": 29.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.619, "end": 29.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "as", "start": 29.639, "end": 29.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.739, "end": 29.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "if", "start": 29.779, "end": 29.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.879, "end": 29.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 29.899, "end": 30.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.0, "end": 30.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 30.039, "end": 30.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.139, "end": 30.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "an", "start": 30.139, "end": 30.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.239, "end": 30.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "entirely", "start": 30.26, "end": 30.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.899, "end": 30.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "different", "start": 30.959, "end": 31.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.199, "end": 31.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "person,", "start": 31.26, "end": 31.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.719, "end": 31.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "disinterested", "start": 31.959, "end": 32.62, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.62, "end": 32.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 32.639, "end": 32.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.72, "end": 32.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "their", "start": 32.739, "end": 32.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.86, "end": 32.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "surprise.", "start": 32.919, "end": 33.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.539, "end": 33.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Soon,", "start": 33.899, "end": 34.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.319, "end": 34.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 34.5, "end": 34.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.58, "end": 34.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "atmosphere", "start": 34.619, "end": 35.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.08, "end": 35.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "livened", "start": 35.119, "end": 35.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.42, "end": 35.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 35.439, "end": 35.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.559, "end": 35.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "again.", "start": 35.559, "end": 35.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.92, "end": 36.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Someone", "start": 36.18, "end": 36.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.5, "end": 36.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wanted", "start": 36.54, "end": 36.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.779, "end": 36.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 36.799, "end": 36.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.88, "end": 36.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "make", "start": 36.899, "end": 37.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.04, "end": 37.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Zania", "start": 37.059, "end": 37.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.419, "end": 37.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "drink,", "start": 37.439, "end": 37.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.879, "end": 38.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 38.139, "end": 38.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.239, "end": 38.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "she", "start": 38.239, "end": 38.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.38, "end": 38.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "looked", "start": 38.399, "end": 38.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.619, "end": 38.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pitifully", "start": 38.619, "end": 39.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.239, "end": 39.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 39.479, "end": 39.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.58, "end": 39.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 39.619, "end": 40.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.12, "end": 40.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "who", "start": 40.34, "end": 40.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.499, "end": 40.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "promptly", "start": 40.5, "end": 40.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.88, "end": 40.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stepped", "start": 40.959, "end": 41.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.2, "end": 41.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 41.219, "end": 41.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.319, "end": 41.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 41.319, "end": 41.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.419, "end": 41.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shield", "start": 41.479, "end": 41.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.719, "end": 41.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "her", "start": 41.719, "end": 41.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.86, "end": 41.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "from", "start": 41.879, "end": 42.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.02, "end": 42.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 42.04, "end": 42.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.099, "end": 42.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "drink.", "start": 42.139, "end": 42.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.5, "end": 42.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Observing", "start": 42.86, "end": 43.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.36, "end": 43.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 43.379, "end": 43.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.479, "end": 43.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "familiar", "start": 43.479, "end": 43.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.959, "end": 44.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "yet", "start": 44.02, "end": 44.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.219, "end": 44.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "alien", "start": 44.239, "end": 44.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.679, "end": 44.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 44.739, "end": 45.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.239, "end": 45.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 45.539, "end": 45.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.619, "end": 45.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "realized", "start": 45.659, "end": 46.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.04, "end": 46.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 46.119, "end": 46.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.219, "end": 46.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "might", "start": 46.239, "end": 46.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.399, "end": 46.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "never", "start": 46.539, "end": 46.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.779, "end": 46.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "have", "start": 46.86, "end": 46.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.939, "end": 47.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "truly", "start": 47.079, "end": 47.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.419, "end": 47.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "known", "start": 47.52, "end": 47.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.759, "end": 47.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "him.", "start": 47.759, "end": 48.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.039, "end": 48.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "On", "start": 48.18, "end": 48.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.299, "end": 48.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "our", "start": 48.299, "end": 48.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.479, "end": 48.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "third", "start": 48.5, "end": 48.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.68, "end": 48.7, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "anniversary", "start": 48.7, "end": 49.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.299, "end": 49.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "during", "start": 49.34, "end": 49.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.579, "end": 49.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dinner,", "start": 49.639, "end": 50.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.039, "end": 50.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "one", "start": 50.259, "end": 50.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.34, "end": 50.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 50.36, "end": 50.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.419, "end": 50.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 50.419, "end": 50.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.599, "end": 50.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "friends", "start": 50.659, "end": 50.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.919, "end": 50.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "insisted", "start": 50.939, "end": 51.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.379, "end": 51.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 51.379, "end": 51.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.539, "end": 51.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "drink", "start": 51.539, "end": 51.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.839, "end": 51.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "despite", "start": 51.84, "end": 52.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.359, "end": 52.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 52.379, "end": 52.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.559, "end": 52.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "protests", "start": 52.579, "end": 52.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.979, "end": 53.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 53.0, "end": 53.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.119, "end": 53.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "feeling", "start": 53.119, "end": 53.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 53.439, "end": 53.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "unwell.", "start": 53.539, "end": 54.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.02, "end": 54.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 54.439, "end": 54.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.919, "end": 54.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "thinking", "start": 54.919, "end": 55.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.2, "end": 55.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 55.239, "end": 55.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.259, "end": 55.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 55.259, "end": 55.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.479, "end": 55.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 55.52, "end": 55.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.7, "end": 55.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "throwing", "start": 55.719, "end": 56.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.04, "end": 56.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 56.059, "end": 56.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.159, "end": 56.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "temper", "start": 56.18, "end": 56.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.439, "end": 56.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "tantrum,", "start": 56.52, "end": 57.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.019, "end": 57.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "forced", "start": 57.199, "end": 57.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.5, "end": 57.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 57.52, "end": 57.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.58, "end": 57.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 57.619, "end": 57.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.739, "end": 57.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "drink.", "start": 57.739, "end": 58.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.159, "end": 58.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 58.299, "end": 58.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.459, "end": 58.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ended", "start": 58.479, "end": 58.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.7, "end": 58.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 58.719, "end": 58.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.84, "end": 58.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 58.859, "end": 58.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.919, "end": 58.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 58.939, "end": 59.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.039, "end": 59.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hospital", "start": 59.059, "end": 59.499, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.499, "end": 59.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 59.52, "end": 59.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.659, "end": 59.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "night,", "start": 59.719, "end": 59.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.999, "end": 60.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "where", "start": 60.139, "end": 60.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.239, "end": 60.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 60.299, "end": 60.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.419, "end": 60.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "learned", "start": 60.439, "end": 60.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.659, "end": 60.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 60.699, "end": 60.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.799, "end": 60.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "had", "start": 60.819, "end": 60.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.939, "end": 61.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miscarried.", "start": 61.0, "end": 61.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}], "additional_formats": [{"requested_format": "srt", "file_extension": "srt", "content_type": "text/srt", "is_base64_encoded": false, "content": "1\n00:00:00,079 --> 00:00:03,979\n[speaker_0] He furrowed his brows and said, \"Try\nto spruce up a bit when you go out next time.\"\n\n2\n00:00:04,139 --> 00:00:08,619\nUnperturbed, I continued to engage with my phone,\nleaving <PERSON> to awkwardly attempt small\n\n3\n00:00:08,679 --> 00:00:12,939\ntalk for the rest of the journey. Upon arriving at\nthe venue, <PERSON> quickly came over to\n\n4\n00:00:12,960 --> 00:00:17,379\nexplain why he had to draft <PERSON> in as a\ngroomsman, assuring me that <PERSON> would have\n\n5\n00:00:17,399 --> 00:00:21,199\nbrought me along otherwise. He downed three\npenalty drinks to show his regret.\n\n6\n00:00:21,439 --> 00:00:24,979\nI wasn't at all upset. Don't blame yourself. Happy\nwedding.\n\n7\n00:00:25,239 --> 00:00:29,879\nWhile everyone was initially shocked not to see me\nmake a scene as before, I acted as if\n\n8\n00:00:29,899 --> 00:00:33,539\nI was an entirely different person, disinterested\nin their surprise.\n\n9\n00:00:33,899 --> 00:00:35,920\nSoon, the atmosphere livened up again.\n\n10\n00:00:36,180 --> 00:00:40,880\nSomeone wanted to make <PERSON><PERSON> drink, and she looked\npitifully at <PERSON>, who promptly\n\n11\n00:00:40,959 --> 00:00:42,500\nstepped in to shield her from the drink.\n\n12\n00:00:42,860 --> 00:00:47,759\nObserving the familiar yet alien <PERSON>, I realized\nI might never have truly known\n\n13\n00:00:47,759 --> 00:00:52,359\nhim. On our third anniversary during dinner, one\nof his friends insisted I drink despite\n\n14\n00:00:52,379 --> 00:00:57,019\nmy protests of feeling unwell. <PERSON>, thinking I\nwas just throwing a temper tantrum,\n\n15\n00:00:57,199 --> 00:01:00,939\nforced me to drink. I ended up in the hospital\nthat night, where I learned I had\n\n16\n00:01:01,000 --> 00:01:01,659\nmiscarried.\n"}]}