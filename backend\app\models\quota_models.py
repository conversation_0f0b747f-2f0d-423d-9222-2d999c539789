# -*- coding: utf-8 -*-
"""
配额系统数据模型
"""

from sqlalchemy import Column, String, BigInteger, DateTime, <PERSON><PERSON>an, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()

class UserQuota(Base):
    """用户配额表"""
    __tablename__ = "user_quotas"
    
    user_id = Column(String(255), primary_key=True, comment="用户ID")
    quota_balance = Column(BigInteger, nullable=False, default=0, comment="剩余配额(秒)")
    total_quota = Column(BigInteger, nullable=False, default=0, comment="总配额(秒)")
    last_updated = Column(DateTime, default=func.now(), comment="最后更新时间")
    expiry_date = Column(DateTime, nullable=True, comment="到期时间")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")

class QuotaUsageLog(Base):
    """配额使用记录表"""
    __tablename__ = "quota_usage_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(255), nullable=False, comment="用户ID")
    task_id = Column(String(255), nullable=False, comment="任务ID")
    operation_type = Column(String(50), nullable=False, comment="操作类型: check/deduct/refund")
    quota_amount = Column(BigInteger, nullable=False, comment="配额数量(秒)")
    audio_duration = Column(BigInteger, nullable=True, comment="音频时长(秒)")
    balance_before = Column(BigInteger, nullable=False, comment="操作前余额")
    balance_after = Column(BigInteger, nullable=False, comment="操作后余额")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")

class RechargeCard(Base):
    """充值卡表"""
    __tablename__ = "recharge_cards"
    
    card_id = Column(String(255), primary_key=True, comment="卡密ID")
    quota_amount = Column(BigInteger, nullable=False, comment="配额数量(秒)")
    description = Column(String(500), nullable=True, comment="套餐描述")
    is_used = Column(Boolean, default=False, comment="是否已使用")
    used_by = Column(String(255), nullable=True, comment="使用者")
    used_at = Column(DateTime, nullable=True, comment="使用时间")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
