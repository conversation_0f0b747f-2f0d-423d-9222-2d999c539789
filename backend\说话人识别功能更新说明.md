# 说话人识别功能更新说明

## 📋 更新概述

将说话人识别（Speaker Diarization）功能从**固定启用**改为**可选控制**，支持前端开关控制。

## 🔧 技术变更

### 1. 后端模型更新

**文件**: `backend/app/models/request_models.py`

```python
class TranscribeRequest(BaseModel):
    language_code: Optional[str] = Field(default=None, max_length=10)
    tag_audio_events: bool = Field(default=True)
    diarize: bool = Field(default=False, description="是否启用说话人识别")  # 新增
    num_speakers: Optional[int] = Field(default=None, ge=1, le=32)
    timestamps_granularity: TimestampsGranularity = Field(default=TimestampsGranularity.WORD)
    additional_formats: Optional[List[SrtExportOptions]] = Field(default=None)
```

**变更说明**:
- ✅ 新增 `diarize` 参数
- ✅ 默认值设为 `False`（之前固定为 `true`）
- ✅ 添加参数描述

### 2. API接口更新

**文件**: `backend/app/api/transcribe.py`

```python
@router.post("/upload", response_model=TranscribeResponse)
async def upload_audio(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="音频或视频文件"),
    language_code: Optional[str] = Form(None, description="语言代码 (如: zh, en, ja)"),
    tag_audio_events: bool = Form(True, description="是否标记音频事件"),
    diarize: bool = Form(False, description="是否启用说话人识别"),  # 新增
    num_speakers: Optional[int] = Form(None, ge=1, le=32, description="最大说话人数量"),
    timestamps_granularity: str = Form("word", description="时间戳粒度: none/word/character"),
    additional_formats: Optional[str] = Form(None, description="额外格式配置 (JSON字符串)"),
    task_service: TaskService = Depends(get_task_service),
    file_service: FileService = Depends(get_file_service)
):
```

**变更说明**:
- ✅ 新增 `diarize` 表单参数
- ✅ 默认值为 `False`
- ✅ 在请求参数构建中包含 `diarize` 参数

### 3. ElevenLabs客户端更新

**文件**: `backend/app/services/elevenlabs_client.py`

```python
# 构建表单数据
data = aiohttp.FormData()
data.add_field('model_id', self.MODEL_ID)
data.add_field('diarize', str(request_params.diarize).lower())  # 改为可配置
data.add_field('tag_audio_events', str(request_params.tag_audio_events).lower())
data.add_field('timestamps_granularity', request_params.timestamps_granularity.value)
```

**变更说明**:
- ✅ 将固定的 `'true'` 改为 `str(request_params.diarize).lower()`
- ✅ 支持动态控制说话人识别功能

## 🎨 前端界面更新

### 1. 表单控件

**位置**: 音频事件标记和说话人数量之间

```jsx
{/* 说话人识别 */}
<Form.Item
  name="diarize"
  label="说话人识别"
  valuePropName="checked"
  tooltip="是否启用说话人识别功能，识别音频中的不同说话人"
>
  <Switch />
</Form.Item>
```

### 2. 初始值配置

```jsx
initialValues={{
  tag_audio_events: true,
  diarize: false,  // 新增，默认关闭
  timestamps_granularity: 'word',
  additionalFormats: {
    enabled: false,
    maxCharactersPerLine: 50,
    includeSpeakers: false,
    includeTimestamps: true,
    segmentOnSilence: 0.6,
    maxSegmentDuration: 5,
    maxSegmentChars: 90
  }
}}
```

## 📚 API文档更新

### 1. 参数列表

| 参数名 | 类型 | 是否必需 | 默认值 | 说明 | 前端控制 |
|--------|------|----------|--------|------|----------|
| `diarize` | boolean | ❌ | `false` | 说话人识别 | ✅ 开关控制 |

### 2. 参数详细说明

```json
{
  "diarize": false  // 是否启用说话人识别
}
```

**前端实现**: 开关控制
```javascript
<Switch 
  checked={diarize} 
  onChange={setDiarize}
  label="说话人识别 (识别音频中的不同说话人)"
/>
```

## 🔍 功能影响

### 1. 向后兼容性

- ✅ **完全向后兼容**: 现有API调用不受影响
- ✅ **默认行为变更**: 说话人识别默认关闭（之前默认开启）
- ✅ **渐进式升级**: 用户可以选择性启用功能

### 2. 字幕输出变化

**启用说话人识别时** (`diarize: true`):
```srt
1
00:00:00,119 --> 00:00:04,999
[speaker_0] When I woke up, sweating from the pain...

2
00:00:05,099 --> 00:00:09,939
[speaker_1] As a woman, you didn't even know...
```

**关闭说话人识别时** (`diarize: false`):
```srt
1
00:00:00,119 --> 00:00:04,999
When I woke up, sweating from the pain...

2
00:00:05,099 --> 00:00:09,939
As a woman, you didn't even know...
```

## ✅ 测试验证

### 1. 基本功能测试

```python
# 测试默认值
request = TranscribeRequest()
assert request.diarize == False

# 测试设置为True
request = TranscribeRequest(diarize=True)
assert request.diarize == True
```

### 2. API参数验证

```bash
# 验证API接口包含diarize参数
python -c "from app.api.transcribe import upload_audio; import inspect; print('diarize' in inspect.signature(upload_audio).parameters)"
# 输出: True
```

## 🎯 用户体验改进

1. **更灵活的控制**: 用户可以根据需要选择是否启用说话人识别
2. **性能优化**: 不需要说话人识别时可以关闭，提高处理速度
3. **成本控制**: 某些场景下关闭说话人识别可能降低API调用成本
4. **清洁输出**: 单人音频可以关闭说话人标记，获得更清洁的字幕

## 📝 使用建议

### 何时启用说话人识别

- ✅ **多人对话**: 访谈、会议、讨论
- ✅ **播客节目**: 多主持人节目
- ✅ **教学视频**: 师生对话
- ✅ **客服录音**: 客服与客户对话

### 何时关闭说话人识别

- ✅ **单人演讲**: 讲座、演示、独白
- ✅ **音乐视频**: 歌曲、音乐内容
- ✅ **纪录片旁白**: 单一叙述者
- ✅ **个人录音**: 个人笔记、日记

## 🚀 部署说明

1. **后端更新**: 重启后端服务以应用模型和API变更
2. **前端更新**: 更新前端代码以包含新的开关控件
3. **文档同步**: 确保API文档与实际实现保持一致
4. **用户通知**: 通知用户新功能的可用性和使用方法
