import aiohttp
import aiofiles
import os
import random
import json
from typing import Optional, Dict, Any, Callable
from app.models.request_models import TranscribeRequest, SrtExportOptions

class AsyncElevenLabsSTTClient:
    """异步 ElevenLabs STT 客户端"""

    API_URL = "https://api.elevenlabs.io/v1/speech-to-text"
    API_PARAMS = {"allow_unauthenticated": "1"}
    MODEL_ID = "scribe_v1_experimental"  # 固定使用 scribe_v1_experimental

    # 保持原有的请求头配置
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15",
    ]

    ACCEPT_LANGUAGES = [
        "zh-CN,zh;q=0.9,en;q=0.8",
        "en-US,en;q=0.9,es;q=0.8",
        "ja-JP,ja;q=0.9,en;q=0.8",
        "ko-KR,ko;q=0.9,en;q=0.8",
    ]

    BASE_HEADERS = {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "origin": "https://elevenlabs.io",
        "referer": "https://elevenlabs.io/",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
    }

    def __init__(self, session: Optional[aiohttp.ClientSession] = None):
        self.session = session or aiohttp.ClientSession()
        self._own_session = session is None

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._own_session and self.session:
            await self.session.close()

    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        headers = self.BASE_HEADERS.copy()
        headers["user-agent"] = random.choice(self.USER_AGENTS)
        headers["accept-language"] = random.choice(self.ACCEPT_LANGUAGES)
        return headers

    def _detect_mime_type(self, file_path: str) -> str:
        """检测 MIME 类型"""
        ext = os.path.splitext(file_path)[1].lower()
        mime_map = {
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.flac': 'audio/flac',
            '.m4a': 'audio/m4a',
            '.aac': 'audio/aac',
            '.ogg': 'audio/ogg',
            '.mp4': 'video/mp4',
            '.mov': 'video/mov',
        }
        return mime_map.get(ext, 'application/octet-stream')

    async def transcribe_file(
        self,
        file_path: str,
        request_params: TranscribeRequest,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> Dict[str, Any]:
        """转录音频文件"""

        headers = self._build_headers()
        mime_type = self._detect_mime_type(file_path)

        # 构建表单数据
        data = aiohttp.FormData()
        data.add_field('model_id', self.MODEL_ID)
        data.add_field('diarize', 'true')  # 固定为true，启用说话人识别
        data.add_field('tag_audio_events', str(request_params.tag_audio_events).lower())
        # 移除 timestamps_granularity 参数，因为ElevenLabs API可能不支持

        # 可选参数
        if request_params.language_code:
            data.add_field('language_code', request_params.language_code)

        if request_params.num_speakers:
            data.add_field('num_speakers', str(request_params.num_speakers))

        if request_params.additional_formats:
            formats_json = json.dumps([fmt.model_dump() for fmt in request_params.additional_formats])
            data.add_field('additional_formats', formats_json)

        # 添加文件
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()
            data.add_field(
                'file',
                file_content,
                filename=os.path.basename(file_path),
                content_type=mime_type
            )

        # 发送请求
        timeout = aiohttp.ClientTimeout(total=1800)  # 30分钟超时

        async with self.session.post(
            self.API_URL,
            params=self.API_PARAMS,
            headers=headers,
            data=data,
            timeout=timeout
        ) as response:
            response.raise_for_status()
            return await response.json()
