# -*- coding: utf-8 -*-
"""
配额管理API
"""

from fastapi import APIRouter, Depends, HTTPException, Header
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Tuple
from pydantic import BaseModel

from app.services.quota_service import QuotaService
from app.auth.jwt_dependencies import verify_jwt_and_check_quota
from app.core.database import get_db_session

router = APIRouter(prefix="/api/v1/quota", tags=["配额管理"])

class RechargeRequest(BaseModel):
    """充值请求模型"""
    card_id: str

@router.get("/info")
async def get_quota_info(
    authorization: str = Header(None),
    db_session: AsyncSession = Depends(get_db_session)
):
    """获取用户配额信息"""
    from app.core.config import settings
    
    if not settings.ENABLE_QUOTA_SYSTEM:
        return {"error": "配额系统未启用"}
    
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "缺少JWT token",
                "hint": "请在请求头中提供JWT token: Authorization: Bearer <token>"
            }
        )
    
    jwt_token = authorization[7:]  # 移除 "Bearer "
    quota_service = QuotaService(db_session)
    
    # 代理认证
    user_id = await quota_service.verify_jwt_and_get_user(jwt_token)
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "JWT token无效或已过期",
                "hint": "请重新登录获取有效的JWT token"
            }
        )
    
    quota_info = await quota_service.get_user_quota_info(user_id)
    return quota_info

@router.post("/recharge")
async def recharge_quota(
    request: RechargeRequest,
    authorization: str = Header(None),
    db_session: AsyncSession = Depends(get_db_session)
):
    """使用卡密充值配额"""
    from app.core.config import settings
    
    if not settings.ENABLE_QUOTA_SYSTEM:
        return {"error": "配额系统未启用"}
    
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "缺少JWT token",
                "hint": "请在请求头中提供JWT token: Authorization: Bearer <token>"
            }
        )
    
    jwt_token = authorization[7:]  # 移除 "Bearer "
    quota_service = QuotaService(db_session)
    
    # 代理认证
    user_id = await quota_service.verify_jwt_and_get_user(jwt_token)
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "JWT token无效或已过期",
                "hint": "请重新登录获取有效的JWT token"
            }
        )
    
    success, message = await quota_service.recharge_with_card(user_id, request.card_id)
    
    if success:
        return {"success": True, "message": message}
    else:
        raise HTTPException(
            status_code=400, 
            detail={
                "error": "充值失败", 
                "message": message
            }
        )
