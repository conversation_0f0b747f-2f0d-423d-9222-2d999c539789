# -*- coding: utf-8 -*-
"""
管理员认证依赖
"""

import logging
from fastapi import Depends, HTTPException, Header
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.services.quota_service import QuotaService
from app.core.database import get_db_session
from app.core.config import settings

logger = logging.getLogger(__name__)

async def verify_admin_jwt(
    authorization: str = Header(None, description="JWT Token: Bearer <token>"),
    db_session: AsyncSession = Depends(get_db_session)
) -> str:
    """
    管理员JWT认证
    
    基于现有JWT认证，增加管理员权限检查
    
    Args:
        authorization: JWT token
        db_session: 数据库会话
        
    Returns:
        str: 管理员用户ID
        
    Raises:
        HTTPException: 认证失败或权限不足
    """
    # 检查管理员API是否启用
    if not settings.ENABLE_ADMIN_API:
        raise HTTPException(
            status_code=503,
            detail={
                "error": "服务不可用",
                "message": "管理员API未启用",
                "hint": "请联系系统管理员启用管理员API功能"
            }
        )
    
    # 检查配额系统是否启用（管理员API依赖配额系统）
    if not settings.ENABLE_QUOTA_SYSTEM:
        raise HTTPException(
            status_code=503,
            detail={
                "error": "服务不可用",
                "message": "配额系统未启用",
                "hint": "管理员API需要配额系统支持"
            }
        )
    
    # JWT token验证
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "缺少JWT token",
                "hint": "请在请求头中提供JWT token: Authorization: Bearer <token>"
            }
        )
    
    jwt_token = authorization[7:]  # 移除 "Bearer "
    quota_service = QuotaService(db_session)
    
    # 复用现有JWT验证逻辑
    user_id = await quota_service.verify_jwt_and_get_user(jwt_token)
    
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "JWT token无效或已过期",
                "hint": "请重新登录获取有效的JWT token"
            }
        )
    
    # 管理员权限检查
    admin_users = _get_admin_users()
    if user_id not in admin_users:
        logger.warning(f"用户 {user_id} 尝试访问管理员API但权限不足")
        raise HTTPException(
            status_code=403,
            detail={
                "error": "权限不足",
                "message": f"用户 {user_id} 不是管理员",
                "hint": "请使用管理员账户登录"
            }
        )
    
    logger.info(f"管理员 {user_id} 认证成功")
    return user_id

def _get_admin_users() -> List[str]:
    """获取管理员用户列表"""
    admin_users_str = settings.ADMIN_USERS.strip()
    if not admin_users_str:
        return []
    
    # 分割并清理用户名
    admin_users = [user.strip() for user in admin_users_str.split(',') if user.strip()]
    return admin_users

async def get_admin_users_list() -> List[str]:
    """获取管理员用户列表（用于API响应）"""
    return _get_admin_users()

# 便捷的依赖函数，用于需要特定权限的场景
async def verify_admin_with_permission(
    permission: str,
    admin_user: str = Depends(verify_admin_jwt)
) -> str:
    """
    验证管理员权限（预留接口，用于未来权限细分）
    
    Args:
        permission: 所需权限
        admin_user: 管理员用户ID
        
    Returns:
        str: 管理员用户ID
    """
    # 目前所有管理员都有全部权限
    # 未来可以根据需要实现细分权限
    logger.info(f"管理员 {admin_user} 请求权限: {permission}")
    return admin_user
