name: Build and Release Scribe2SRT

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
    
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pyinstaller

      - name: Set asset properties
        shell: bash
        run: |
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            echo "EXECUTABLE_NAME=Scribe2SRT.exe" >> $GITHUB_ENV
            echo "ASSET_NAME=Scribe2SRT-${{ github.ref_name }}-windows-x86_64.zip" >> $GITHUB_ENV
            echo "PYINSTALLER_DATA_SEPARATOR=;" >> $GITHUB_ENV
          elif [[ "${{ matrix.os }}" == "macos-latest" ]]; then
            echo "EXECUTABLE_NAME=Scribe2SRT" >> $GITHUB_ENV
            echo "ASSET_NAME=Scribe2SRT-${{ github.ref_name }}-macos-x86_64.tar.gz" >> $GITHUB_ENV
            echo "PYINSTALLER_DATA_SEPARATOR=:" >> $GITHUB_ENV
          else
            echo "EXECUTABLE_NAME=Scribe2SRT" >> $GITHUB_ENV
            echo "ASSET_NAME=Scribe2SRT-${{ github.ref_name }}-linux-x86_64.tar.gz" >> $GITHUB_ENV
            echo "PYINSTALLER_DATA_SEPARATOR=:" >> $GITHUB_ENV
          fi

      - name: Build with PyInstaller
        shell: bash
        run: |
          pyinstaller --onefile --windowed --noconsole \
            --name "${{ env.EXECUTABLE_NAME }}" \
            --add-data "settings.json${{ env.PYINSTALLER_DATA_SEPARATOR }}." \
            app.py

      - name: Package release (Windows)
        if: matrix.os == 'windows-latest'
        run: Compress-Archive -Path dist/${{ env.EXECUTABLE_NAME }} -DestinationPath ${{ env.ASSET_NAME }}
        shell: pwsh

      - name: Package release (Linux/macOS)
        if: matrix.os != 'windows-latest'
        run: tar -czvf ${{ env.ASSET_NAME }} -C dist ${{ env.EXECUTABLE_NAME }}

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.ASSET_NAME }}
          path: ${{ env.ASSET_NAME }}

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    permissions:
      contents: write
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Create Release and Upload Assets
        uses: softprops/action-gh-release@v2
        with:
          files: artifacts/*/*