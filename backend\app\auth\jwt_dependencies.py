# -*- coding: utf-8 -*-
"""
JWT认证依赖
"""

import logging
from fastapi import Depends, HTTPException, Header
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Tuple

from app.services.quota_service import QuotaService
from app.core.database import get_db_session
from app.core.config import settings

logger = logging.getLogger(__name__)

async def verify_jwt_and_check_quota(
    authorization: str = Header(None, description="JWT Token: Bearer <token>"),
    estimated_duration: int = 300,  # 默认预估5分钟
    db_session: AsyncSession = Depends(get_db_session)
) -> Tuple[str, QuotaService]:
    """
    JWT认证 + 配额预检查

    Returns:
        Tuple[str, QuotaService]: (用户ID, 配额服务实例)
    """
    if not settings.ENABLE_QUOTA_SYSTEM:
        # 如果配额系统未启用，返回默认用户
        return "default_user", QuotaService(db_session)

    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "缺少JWT token",
                "hint": "请在请求头中提供JWT token: Authorization: Bearer <token>"
            }
        )

    jwt_token = authorization[7:]  # 移除 "Bearer "
    quota_service = QuotaService(db_session)

    # 代理认证
    user_id = await quota_service.verify_jwt_and_get_user(jwt_token)

    if not user_id:
        raise HTTPException(
            status_code=401,
            detail={
                "error": "认证失败",
                "message": "JWT token无效或已过期",
                "hint": "请重新登录获取有效的JWT token"
            }
        )
    
    # 配额预检查
    is_sufficient, current_balance, message = await quota_service.check_quota_availability(
        user_id, estimated_duration
    )
    
    if not is_sufficient:
        raise HTTPException(
            status_code=402,  # Payment Required
            detail={
                "error": "配额不足",
                "message": message,
                "current_balance": current_balance,
                "required_duration": estimated_duration,
                "hint": "请充值后再试"
            }
        )
    
    return user_id, quota_service

# 便捷的依赖函数，用于不同预估时长的场景
def create_quota_dependency(estimated_minutes: int = 5):
    """创建指定预估时长的配额依赖"""
    async def quota_dependency(
        authorization: str = Header(None),
        db_session: AsyncSession = Depends(get_db_session)
    ) -> Tuple[str, QuotaService]:
        return await verify_jwt_and_check_quota(
            authorization, 
            estimated_minutes * 60,  # 转换为秒
            db_session
        )
    return quota_dependency
