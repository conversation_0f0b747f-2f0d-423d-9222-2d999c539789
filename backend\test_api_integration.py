#!/usr/bin/env python3
"""
API集成测试脚本
测试完整的API流程，包括SRT文件处理
"""

import sys
import os
import asyncio
import json
import tempfile
import aiohttp
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_api_endpoints():
    """测试API接口"""
    print("🧪 测试API接口...")
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试根路径
            async with session.get(f"{base_url}/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 根路径测试通过: {data['message']}")
                else:
                    print(f"❌ 根路径测试失败: {response.status}")
                    return False
            
            # 测试健康检查（可能会失败，因为没有Redis）
            try:
                async with session.get(f"{base_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 健康检查通过: {data['status']}")
                    else:
                        print(f"⚠️  健康检查失败: {response.status} (可能是Redis未启动)")
            except Exception as e:
                print(f"⚠️  健康检查异常: {e} (可能是Redis未启动)")
            
            # 测试API文档
            async with session.get(f"{base_url}/docs") as response:
                if response.status == 200:
                    print("✅ API文档可访问")
                else:
                    print(f"❌ API文档访问失败: {response.status}")
                    return False
            
            return True
            
        except aiohttp.ClientConnectorError:
            print("❌ 无法连接到API服务器，请确保服务器正在运行")
            return False
        except Exception as e:
            print(f"❌ API测试异常: {e}")
            return False

def create_test_audio_file():
    """创建测试音频文件（模拟）"""
    # 创建一个小的测试文件
    test_content = b"RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x02\x00\x44\xac\x00\x00\x10\xb1\x02\x00\x04\x00\x10\x00data\x00\x08\x00\x00"
    
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as f:
        f.write(test_content)
        return f.name

async def test_upload_endpoint():
    """测试文件上传接口"""
    print("\n🧪 测试文件上传接口...")
    
    base_url = "http://localhost:8000"
    upload_url = f"{base_url}/api/v1/transcribe/upload"
    
    # 创建测试文件
    test_file_path = create_test_audio_file()
    
    try:
        async with aiohttp.ClientSession() as session:
            # 准备表单数据
            data = aiohttp.FormData()
            data.add_field('language_code', 'zh')
            data.add_field('tag_audio_events', 'true')
            data.add_field('timestamps_granularity', 'word')
            
            # 添加额外格式配置
            additional_formats = json.dumps([{
                "format": "srt",
                "max_characters_per_line": 50,
                "include_speakers": True
            }])
            data.add_field('additional_formats', additional_formats)
            
            # 添加文件
            with open(test_file_path, 'rb') as f:
                data.add_field('file', f, filename='test.wav', content_type='audio/wav')
                
                async with session.post(upload_url, data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ 文件上传成功")
                        print(f"   任务ID: {result.get('task_id', 'N/A')}")
                        print(f"   状态: {result.get('status', 'N/A')}")
                        print(f"   消息: {result.get('message', 'N/A')}")
                        return result.get('task_id')
                    else:
                        error_text = await response.text()
                        print(f"❌ 文件上传失败: {response.status}")
                        print(f"   错误信息: {error_text}")
                        return None
    
    except Exception as e:
        print(f"❌ 上传测试异常: {e}")
        return None
    
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
        except:
            pass

async def test_status_endpoint(task_id):
    """测试状态查询接口"""
    if not task_id:
        print("⚠️  跳过状态查询测试（没有有效的任务ID）")
        return False
    
    print(f"\n🧪 测试状态查询接口 (任务ID: {task_id})...")
    
    base_url = "http://localhost:8000"
    status_url = f"{base_url}/api/v1/transcribe/status/{task_id}"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(status_url) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 状态查询成功")
                    print(f"   任务ID: {result.get('task_id', 'N/A')}")
                    print(f"   状态: {result.get('status', 'N/A')}")
                    print(f"   进度: {result.get('progress', 'N/A')}%")
                    print(f"   消息: {result.get('message', 'N/A')}")
                    
                    if result.get('srt_files'):
                        print(f"   SRT文件数量: {len(result['srt_files'])}")
                        for srt_file in result['srt_files']:
                            print(f"     - {srt_file.get('filename', 'N/A')}")
                            print(f"       下载URL: {srt_file.get('download_url', 'N/A')}")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 状态查询失败: {response.status}")
                    print(f"   错误信息: {error_text}")
                    return False
    
    except Exception as e:
        print(f"❌ 状态查询异常: {e}")
        return False

def check_server_running():
    """检查服务器是否运行"""
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8000))
        sock.close()
        return result == 0
    except:
        return False

async def main():
    """主测试函数"""
    print("🚀 开始API集成测试\n")
    
    # 检查服务器是否运行
    if not check_server_running():
        print("❌ API服务器未运行，请先启动服务器:")
        print("   python start_server.py")
        print("\n或者使用Docker:")
        print("   docker-compose up -d")
        return False
    
    print("✅ 检测到API服务器正在运行\n")
    
    tests = [
        ("API基础接口", test_api_endpoints),
    ]
    
    # 如果有Redis，可以测试完整流程
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ 检测到Redis服务，将测试完整流程\n")
        
        # 添加完整流程测试
        async def test_full_workflow():
            task_id = await test_upload_endpoint()
            if task_id:
                # 等待一下再查询状态
                await asyncio.sleep(1)
                return await test_status_endpoint(task_id)
            return False
        
        tests.append(("完整工作流程", test_full_workflow))
        
    except:
        print("⚠️  Redis服务未运行，只测试基础接口\n")
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {name} 测试通过\n")
            else:
                print(f"❌ {name} 测试失败\n")
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}\n")
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有API集成测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    asyncio.run(main())
