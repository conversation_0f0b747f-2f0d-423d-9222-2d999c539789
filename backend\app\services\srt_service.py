import os
import aiofiles
from typing import Dict, Any, List, Optional
from pathlib import Path
from app.core.config import settings

class SrtService:
    """SRT字幕文件处理服务"""
    
    def __init__(self):
        self.srt_dir = Path(settings.UPLOAD_DIR) / "srt_files"
        self.srt_dir.mkdir(exist_ok=True)
    
    async def process_additional_formats(
        self, 
        task_id: str, 
        api_response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        处理API响应中的additional_formats，生成SRT文件
        
        Args:
            task_id: 任务ID
            api_response: ElevenLabs API响应
            
        Returns:
            包含文件信息的处理结果
        """
        result = {
            "transcription": api_response,
            "srt_files": []
        }
        
        additional_formats = api_response.get("additional_formats", [])
        
        if not additional_formats:
            return result
        
        for format_data in additional_formats:
            if format_data.get("requested_format") == "srt":
                srt_info = await self._save_srt_file(task_id, format_data)
                if srt_info:
                    result["srt_files"].append(srt_info)
        
        return result
    
    async def _save_srt_file(
        self, 
        task_id: str, 
        format_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        保存SRT文件到磁盘
        
        Args:
            task_id: 任务ID
            format_data: SRT格式数据
            
        Returns:
            SRT文件信息
        """
        try:
            content = format_data.get("content", "")
            if not content:
                return None
            
            # 生成文件名
            file_extension = format_data.get("file_extension", "srt")
            filename = f"{task_id}.{file_extension}"
            file_path = self.srt_dir / filename
            
            # 保存文件
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            # 计算文件大小
            file_size = os.path.getsize(file_path)
            
            # 解析SRT内容获取时长
            duration = self._parse_srt_duration(content)
            
            return {
                "filename": filename,
                "file_path": str(file_path),
                "download_url": f"/api/v1/transcribe/download/{task_id}/{filename}",
                "content_type": format_data.get("content_type", "text/srt"),
                "file_size": file_size,
                "duration": duration,
                "is_base64_encoded": format_data.get("is_base64_encoded", False)
            }
            
        except Exception as e:
            print(f"保存SRT文件失败: {e}")
            return None
    
    def _parse_srt_duration(self, srt_content: str) -> Optional[float]:
        """
        从SRT内容中解析总时长
        
        Args:
            srt_content: SRT文件内容
            
        Returns:
            总时长（秒）
        """
        try:
            lines = srt_content.strip().split('\n')
            last_timestamp_line = None
            
            # 找到最后一个时间戳行
            for line in reversed(lines):
                if '-->' in line:
                    last_timestamp_line = line
                    break
            
            if not last_timestamp_line:
                return None
            
            # 解析结束时间
            end_time_str = last_timestamp_line.split('-->')[1].strip()
            return self._parse_srt_time(end_time_str)
            
        except Exception:
            return None
    
    def _parse_srt_time(self, time_str: str) -> float:
        """
        解析SRT时间格式为秒数
        
        Args:
            time_str: SRT时间格式 (HH:MM:SS,mmm)
            
        Returns:
            秒数
        """
        try:
            # 格式: 00:01:01,659
            time_part, ms_part = time_str.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
            return total_seconds
            
        except Exception:
            return 0.0
    
    async def get_srt_file(self, task_id: str, filename: str) -> Optional[str]:
        """
        获取SRT文件路径
        
        Args:
            task_id: 任务ID
            filename: 文件名
            
        Returns:
            文件路径或None
        """
        file_path = self.srt_dir / filename
        
        if file_path.exists() and filename.startswith(task_id):
            return str(file_path)
        
        return None
    
    async def cleanup_task_files(self, task_id: str) -> None:
        """
        清理任务相关的SRT文件
        
        Args:
            task_id: 任务ID
        """
        try:
            for file_path in self.srt_dir.glob(f"{task_id}.*"):
                file_path.unlink()
        except Exception as e:
            print(f"清理SRT文件失败: {e}")
    
    def get_srt_statistics(self, srt_content: str) -> Dict[str, Any]:
        """
        获取SRT文件统计信息
        
        Args:
            srt_content: SRT文件内容
            
        Returns:
            统计信息
        """
        try:
            lines = srt_content.strip().split('\n')
            
            # 计算字幕条数
            subtitle_count = 0
            total_chars = 0
            
            for line in lines:
                if line.strip() and not line.strip().isdigit() and '-->' not in line:
                    subtitle_count += 1
                    total_chars += len(line.strip())
            
            duration = self._parse_srt_duration(srt_content)
            
            return {
                "subtitle_count": subtitle_count,
                "total_characters": total_chars,
                "duration": duration,
                "average_chars_per_subtitle": total_chars / subtitle_count if subtitle_count > 0 else 0
            }
            
        except Exception:
            return {
                "subtitle_count": 0,
                "total_characters": 0,
                "duration": None,
                "average_chars_per_subtitle": 0
            }
