# 管理员API接口文档

## 🎯 概述

管理员API提供完整的卡密管理、用户管理和系统统计功能，基于JWT认证，支持通过Web API进行系统管理操作。

## 🔐 认证方式

### JWT管理员认证

管理员API使用JWT认证，复用现有的Worker验证机制，通过管理员用户列表控制权限。

#### 认证流程
```
前端JWT Token → 后端验证 → Worker验证 → 用户名提取 → 管理员权限检查 → API访问
```

#### 配置要求

在 `.env` 文件中配置：

```env
# 启用管理员API
ENABLE_ADMIN_API=true

# 管理员用户列表（逗号分隔）
ADMIN_USERS=555,admin_user,super_admin,manager_001

# 配额系统必须启用（管理员API依赖）
ENABLE_QUOTA_SYSTEM=true
```

#### 认证头格式

```http
Authorization: Bearer <jwt_token>
```

#### 示例调用

```bash
curl -X GET http://localhost:8000/api/v1/admin/stats/admin-info \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 📋 API端点总览

### 卡密管理 (`/api/v1/admin/cards`)
- `GET /cards` - 查看卡密列表
- `POST /cards` - 创建单张卡密
- `POST /cards/batch` - 批量创建卡密
- `GET /cards/{card_id}` - 查看卡密详情

### 用户管理 (`/api/v1/admin/users`)
- `GET /users` - 查看用户列表
- `GET /users/{user_id}` - 查看用户详情
- `PATCH /users/{user_id}/quota` - 修改用户配额
- `GET /users/{user_id}/logs` - 查看用户使用日志

### 统计信息 (`/api/v1/admin/stats`)
- `GET /stats/overview` - 系统概览统计
- `GET /stats/quota` - 配额使用统计
- `GET /stats/cards` - 卡密统计
- `GET /stats/users` - 用户统计
- `GET /stats/admin-info` - 管理员信息

### 日志查询 (`/api/v1/admin/logs`)
- `GET /logs` - 查看使用日志
- `GET /logs/export` - 导出使用日志
- `GET /logs/summary` - 日志统计摘要

## 🎫 卡密管理API

### 查看卡密列表

**端点**: `GET /api/v1/admin/cards`

**查询参数**:
- `status`: 卡密状态 (`all`|`unused`|`used`，默认: `all`)
- `limit`: 每页数量 (1-200，默认: 50)
- `offset`: 偏移量 (默认: 0)
- `search`: 搜索关键词（卡密ID或描述）
- `quota_min`: 最小配额过滤（小时）
- `quota_max`: 最大配额过滤（小时）

**示例请求**:
```bash
curl -X GET "http://localhost:8000/api/v1/admin/cards?status=unused&limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "cards": [
      {
        "card_id": "12345678-1234-1234-1234-123456789abc",
        "quota_amount": 36000,
        "quota_hours": 10.0,
        "description": "月套餐-10小时",
        "is_used": false,
        "used_by": null,
        "used_at": null,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "total": 150,
      "limit": 10,
      "offset": 0,
      "has_next": true
    },
    "summary": {
      "total_cards": 150,
      "unused_cards": 120,
      "used_cards": 30,
      "total_quota_seconds": 5400000,
      "used_quota_seconds": 1080000
    }
  }
}
```

### 创建卡密

**端点**: `POST /api/v1/admin/cards`

**请求体**:
```json
{
  "quota_hours": 10,
  "description": "月套餐-10小时"
}
```

**参数说明**:
- `quota_hours`: 配额小时数 (0-1000)
- `description`: 卡密描述 (可选，最大200字符)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "card_id": "12345678-1234-1234-1234-123456789abc",
    "quota_amount": 36000,
    "quota_hours": 10.0,
    "description": "月套餐-10小时",
    "created_at": "2024-01-15T10:30:00Z"
  },
  "message": "充值卡创建成功，配额: 10小时"
}
```

### 批量创建卡密

**端点**: `POST /api/v1/admin/cards/batch`

**请求体**:
```json
{
  "quota_hours": 10,
  "description": "批量月套餐-10小时",
  "count": 100
}
```

**参数说明**:
- `quota_hours`: 配额小时数 (0-1000)
- `description`: 卡密描述 (可选)
- `count`: 创建数量 (1-1000)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "created_count": 100,
    "quota_hours": 10,
    "total_quota_seconds": 3600000,
    "description": "批量月套餐-10小时",
    "card_ids": [
      "12345678-1234-1234-1234-123456789abc",
      "87654321-4321-4321-4321-cba987654321"
    ]
  },
  "message": "批量创建100张卡密成功"
}
```

## 👥 用户管理API

### 查看用户列表

**端点**: `GET /api/v1/admin/users`

**查询参数**:
- `limit`: 每页数量 (1-200，默认: 50)
- `offset`: 偏移量 (默认: 0)
- `search`: 搜索用户ID
- `status`: 用户状态 (`active`|`exhausted`)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "user_id": "555",
        "quota_balance": 30600,
        "quota_balance_hours": 8.5,
        "total_quota": 36000,
        "total_quota_hours": 10.0,
        "expiry_date": null,
        "status": "active",
        "last_updated": "2024-01-15T14:30:00Z",
        "total_usage": 5400,
        "total_usage_hours": 1.5,
        "recharge_count": 1,
        "last_activity": "2024-01-15T16:45:00Z"
      }
    ],
    "pagination": {
      "total": 25,
      "limit": 50,
      "offset": 0,
      "has_next": false
    },
    "summary": {
      "total_users": 25,
      "active_users": 20,
      "exhausted_users": 5,
      "total_quota_distributed": 900000,
      "total_quota_used": 450000
    }
  }
}
```

### 修改用户配额

**端点**: `PATCH /api/v1/admin/users/{user_id}/quota`

**请求体**:
```json
{
  "operation": "add",
  "quota_hours": 5,
  "reason": "管理员手动调整"
}
```

**参数说明**:
- `operation`: 操作类型 (`add`|`set`|`subtract`)
- `quota_hours`: 配额小时数 (0-10000)
- `reason`: 操作原因 (可选，最大200字符)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user_id": "555",
    "operation": "add",
    "quota_change_hours": 5,
    "old_balance_hours": 8.5,
    "new_balance_hours": 13.5,
    "old_total_hours": 10.0,
    "new_total_hours": 15.0,
    "reason": "管理员手动调整"
  },
  "message": "用户 555 配额修改成功"
}
```

## 📊 统计信息API

### 系统概览统计

**端点**: `GET /api/v1/admin/stats/overview`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "cards": {
      "total": 500,
      "unused": 350,
      "used": 150,
      "total_value_hours": 5000
    },
    "users": {
      "total": 125,
      "active": 100,
      "exhausted": 25
    },
    "quota": {
      "total_distributed": 18000000,
      "total_used": 9000000,
      "utilization_rate": 0.5
    },
    "activity": {
      "today_recharges": 15,
      "today_usage_hours": 45.5,
      "this_month_new_users": 20
    }
  },
  "message": "统计信息获取成功"
}
```

### 管理员信息

**端点**: `GET /api/v1/admin/stats/admin-info`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current_admin": "555",
    "admin_users": ["555", "admin_user", "super_admin", "manager_001"],
    "admin_count": 4,
    "system_info": {
      "admin_api_enabled": true,
      "quota_system_enabled": true
    }
  },
  "message": "管理员信息获取成功"
}
```

## 📝 使用日志API

### 查看使用日志

**端点**: `GET /api/v1/admin/logs`

**查询参数**:
- `user_id`: 用户ID过滤 (可选)
- `limit`: 数量限制 (1-1000，默认: 100)
- `offset`: 偏移量 (默认: 0)
- `start_date`: 开始时间 (ISO格式)
- `end_date`: 结束时间 (ISO格式)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "log_id": 1,
        "user_id": "555",
        "task_id": "abc123-def456",
        "quota_used": 300,
        "quota_used_hours": 0.083,
        "file_duration": 280,
        "created_at": "2024-01-15T16:45:00Z"
      }
    ],
    "pagination": {
      "total": 1500,
      "limit": 100,
      "offset": 0,
      "has_next": true
    }
  },
  "message": "日志查询成功"
}
```

## 🚨 错误处理

### 认证失败

**状态码**: `401 Unauthorized`

```json
{
  "detail": {
    "error": "认证失败",
    "message": "JWT token无效或已过期",
    "hint": "请重新登录获取有效的JWT token"
  }
}
```

### 权限不足

**状态码**: `403 Forbidden`

```json
{
  "detail": {
    "error": "权限不足",
    "message": "用户 555 不是管理员",
    "hint": "请使用管理员账户登录"
  }
}
```

### 服务不可用

**状态码**: `503 Service Unavailable`

```json
{
  "detail": {
    "error": "服务不可用",
    "message": "管理员API未启用",
    "hint": "请联系系统管理员启用管理员API功能"
  }
}
```

### 参数错误

**状态码**: `422 Unprocessable Entity`

```json
{
  "detail": [
    {
      "type": "greater_than",
      "loc": ["body", "quota_hours"],
      "msg": "Input should be greater than 0",
      "input": -1
    }
  ]
}
```

## 🧪 测试示例

### 完整工作流程测试

#### 1. 验证管理员身份

```bash
curl -X GET http://localhost:8000/api/v1/admin/stats/admin-info \
  -H "Authorization: Bearer <jwt_token>"
```

#### 2. 查看系统统计

```bash
curl -X GET http://localhost:8000/api/v1/admin/stats/overview \
  -H "Authorization: Bearer <jwt_token>"
```

#### 3. 创建充值卡

```bash
curl -X POST http://localhost:8000/api/v1/admin/cards \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"quota_hours": 10, "description": "测试卡密"}'
```

#### 4. 查看卡密列表

```bash
curl -X GET "http://localhost:8000/api/v1/admin/cards?status=unused&limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

#### 5. 查看用户列表

```bash
curl -X GET "http://localhost:8000/api/v1/admin/users?limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

#### 6. 修改用户配额

```bash
curl -X PATCH http://localhost:8000/api/v1/admin/users/555/quota \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"operation": "add", "quota_hours": 5, "reason": "测试调整"}'
```

#### 7. 查看使用日志

```bash
curl -X GET "http://localhost:8000/api/v1/admin/logs?limit=20" \
  -H "Authorization: Bearer <jwt_token>"
```

## 🔧 部署配置

### 环境变量配置

```env
# 管理员API配置
ENABLE_ADMIN_API=true
ADMIN_USERS=555,admin_user,super_admin,manager_001

# 依赖配置（必须启用）
ENABLE_QUOTA_SYSTEM=true
WORKER_BASE_URL=https://your-worker.domain.com
WORKER_VERIFY_ENDPOINT=/api/auth/verify
```

### 启动验证

启动服务后，检查日志确认管理员API已启用：

```
2024-01-15 10:30:00 - app.main - INFO - 管理员API已启用
```

### API文档访问

启动服务后，可通过以下地址查看完整API文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

在文档中可以找到所有管理员API端点，并可以直接测试。

## 🔒 安全考虑

### 1. 认证安全
- **JWT验证**: 通过Worker代理验证，确保token有效性
- **权限控制**: 只有配置的管理员用户才能访问
- **会话管理**: JWT过期时间由Worker控制

### 2. 操作审计
- **操作日志**: 所有管理员操作都会记录日志
- **用户追踪**: 记录具体的管理员用户ID
- **时间戳**: 精确记录操作时间

### 3. 数据保护
- **分页限制**: 防止大量数据泄露
- **敏感信息**: 卡密ID等敏感信息有适当保护
- **访问控制**: 严格的权限验证

### 4. 系统安全
- **条件启用**: 可以通过配置禁用管理员API
- **依赖检查**: 确保配额系统正常运行
- **错误处理**: 详细的错误信息和提示

## 📋 最佳实践

### 1. 管理员账户管理
- 使用强JWT token
- 定期更新管理员用户列表
- 为不同管理员分配不同的用户ID

### 2. 卡密管理
- 批量创建时注意数量限制
- 定期清理已使用的卡密记录
- 为不同套餐使用清晰的描述

### 3. 用户管理
- 谨慎使用配额修改功能
- 记录配额调整的原因
- 定期检查用户配额使用情况

### 4. 监控和维护
- 定期查看系统统计信息
- 监控用户活跃度和配额使用
- 及时处理异常情况

## 📞 技术支持

如果在使用管理员API过程中遇到问题，请：

1. 检查环境变量配置是否正确
2. 确认JWT token是否有效
3. 验证用户是否在管理员列表中
4. 查看服务器日志获取详细错误信息
5. 参考错误处理章节的解决方案

## 📝 更新日志

- **v1.0.0**: 初始版本
  - 完整的卡密管理API
  - 用户管理和配额调整功能
  - 系统统计和日志查询
  - JWT管理员认证机制
```
