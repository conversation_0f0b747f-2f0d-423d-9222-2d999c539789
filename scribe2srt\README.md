# Scribe2SRT

Scribe2SRT 是一款专业的音视频转字幕工具。通过集成 ElevenLabs 语音识别技术和智能字幕分割算法，让字幕制作变得简单高效。

## 🚀 主要特性

- 🎯 **高质量转录**：基于 ElevenLabs 先进的语音识别技术
- 🌍 **多语言支持**：支持中文、英文、日文、韩文等多种语言
- 📝 **专业字幕标准**：遵循 Netflix 等行业标准的字幕制作规范
- ⚡ **智能分割算法**：基于标点符号优先级的语义分割，保持句子完整性
- 🔄 **智能重试机制**：失败时自动保留临时文件，重试时快速恢复
- 🎨 **用户友好界面**：简洁直观的图形用户界面，支持拖拽操作
- 📊 **实时进度反馈**：清晰的进度显示和状态提示

## 💻 安装使用

### 快速开始
1. 前往 [Releases 页面](https://github.com/cylind/scribe2srt/releases) 下载最新版本
2. 解压后直接运行程序
3. **推荐安装 FFmpeg**：用于视频文件处理，提升兼容性和处理效率

<details>
<summary>从源码运行（点击展开）</summary>

#### 安装步骤
1. **下载项目**
   ```bash
   git clone https://github.com/your-username/scribe2srt.git
   cd scribe2srt
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python app.py
   ```

</details>

## 📖 使用方法

### 基本操作流程
1. **选择输入文件**
   - 点击"选择文件"按钮或直接拖拽文件到程序窗口
   - 支持三种输入类型：
     - **音频文件**：支持所有常见音频格式（MP3, WAV, FLAC, M4A, AAC, OGG 等）
     - **视频文件**：支持所有常见视频格式（MP4, MOV, MKV, AVI, FLV, WEBM 等）
     - **JSON 转录文件**：ElevenLabs 格式的转录数据

2. **配置处理选项**
   - **语言选择**：选择源语言或使用"自动检测"
   - **音频事件标记**：选择是否标记非语音事件（如笑声、掌声等）

3. **开始处理**
   - 点击"生成字幕"按钮开始转录
   - 程序会显示详细的处理进度

4. **获取结果**
   - 处理完成后，SRT 字幕文件会自动保存到源文件同目录
   - 程序会显示输出文件路径



### 字幕质量标准

本工具遵循专业字幕制作标准：

- **时长控制**：最短 0.83 秒，最长 7.0 秒
- **字符密度**：CJK 语言每秒最多 11 字符，拉丁语言每秒最多 15 字符
- **行长限制**：CJK 语言每行最多 25 字符，拉丁语言每行最多 42 字符
- **语义完整性**：优先保持句子完整，基于标点符号优先级分割

## ⚙️ 高级设置

### 字幕参数调整
通过"字幕设置"菜单可以调整：
- 字幕显示时长和间隔
- 字符密度限制
- 每行字符数限制

### 大文件处理
- 自动分段处理长文件（90分钟以上）
- 支持并发处理，提升处理速度
- 智能重试机制，确保处理成功

## 🔧 技术特点

### 智能分割算法
- **两阶段处理**：句子预分割 + 智能合并
- **标点符号优先**：基于语言学规律的分割策略
- **语义完整性**：避免破坏句子结构
- **多语言优化**：针对不同语言的差异化处理

## 📄 许可证

本项目采用 MIT 许可证。

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐ Star！**

</div>
