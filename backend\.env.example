# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
MAX_UPLOAD_SIZE=1073741824  # 1GB
UPLOAD_DIR=/tmp/uploads
CHUNK_DIR=/tmp/chunks

# 并发控制
MAX_CONCURRENT_TASKS=3
MAX_REQUESTS_PER_MINUTE=30

# ElevenLabs API 配置
ELEVENLABS_API_URL=https://api.elevenlabs.io/v1/speech-to-text
ELEVENLABS_TIMEOUT=1800

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# 安全配置
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=["http://localhost:3000", "http://localhost:4000"]

# JWT认证系统配置
WORKER_BASE_URL=https://your-worker.domain.com
WORKER_VERIFY_ENDPOINT=/api/auth/verify
DATABASE_URL=sqlite+aiosqlite:///./quota.db
ENABLE_QUOTA_SYSTEM=true

# 管理员API配置
ENABLE_ADMIN_API=true
ADMIN_USERS=admin_user,super_admin,manager_001

# 应用配置
APP_NAME=ElevenLabs STT Backend
APP_VERSION=1.0.0
DEBUG=false
