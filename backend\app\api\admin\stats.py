# -*- coding: utf-8 -*-
"""
管理员API - 统计信息
"""

import logging
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.admin_dependencies import verify_admin_jwt, get_admin_users_list
from app.core.database import get_db_session
from app.services.admin_service import AdminService
from app.models.admin_models import AdminResponse, SystemStats

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/stats", tags=["管理员-统计信息"])

@router.get("/overview", response_model=AdminResponse, summary="系统概览统计")
async def get_system_overview(
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    获取系统概览统计
    
    包含：
    - 卡密统计（总数、未使用、已使用、总价值）
    - 用户统计（总数、活跃、配额耗尽）
    - 配额统计（总分发、总使用、利用率）
    - 活动统计（今日充值、今日使用、本月新用户）
    """
    try:
        admin_service = AdminService(db_session)
        stats = await admin_service.get_system_stats()
        
        logger.info(f"管理员 {admin_user} 查看系统概览统计")
        
        return AdminResponse(
            success=True,
            data=stats.dict(),
            message="统计信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "统计失败",
                "message": "获取系统统计时发生错误",
                "hint": "请稍后重试或联系管理员"
            }
        )

@router.get("/quota", response_model=AdminResponse, summary="配额使用统计")
async def get_quota_stats(
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    获取配额使用统计
    
    详细的配额分发和使用情况
    """
    try:
        admin_service = AdminService(db_session)
        stats = await admin_service.get_system_stats()
        
        quota_stats = {
            "quota_overview": stats.quota,
            "distribution": {
                "total_cards_value": stats.cards["total_value_hours"],
                "distributed_to_users": stats.quota["total_distributed"] / 3600,
                "pending_distribution": max(0, stats.cards["total_value_hours"] - stats.quota["total_distributed"] / 3600)
            },
            "usage": {
                "total_used_hours": stats.quota["total_used"] / 3600,
                "utilization_rate": stats.quota["utilization_rate"],
                "remaining_hours": (stats.quota["total_distributed"] - stats.quota["total_used"]) / 3600
            }
        }
        
        logger.info(f"管理员 {admin_user} 查看配额统计")
        
        return AdminResponse(
            success=True,
            data=quota_stats,
            message="配额统计获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取配额统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "统计失败",
                "message": "获取配额统计时发生错误"
            }
        )

@router.get("/cards", response_model=AdminResponse, summary="卡密统计")
async def get_cards_stats(
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    获取卡密统计信息
    
    卡密的创建、使用情况统计
    """
    try:
        admin_service = AdminService(db_session)
        stats = await admin_service.get_system_stats()
        
        cards_stats = {
            "overview": stats.cards,
            "usage_rate": (
                stats.cards["used"] / stats.cards["total"]
                if stats.cards["total"] > 0 else 0
            ),
            "average_value": (
                stats.cards["total_value_hours"] / stats.cards["total"]
                if stats.cards["total"] > 0 else 0
            )
        }
        
        logger.info(f"管理员 {admin_user} 查看卡密统计")
        
        return AdminResponse(
            success=True,
            data=cards_stats,
            message="卡密统计获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取卡密统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "统计失败",
                "message": "获取卡密统计时发生错误"
            }
        )

@router.get("/users", response_model=AdminResponse, summary="用户统计")
async def get_users_stats(
    admin_user: str = Depends(verify_admin_jwt),
    db_session: AsyncSession = Depends(get_db_session)
):
    """
    获取用户统计信息
    
    用户的活跃情况、配额使用情况统计
    """
    try:
        admin_service = AdminService(db_session)
        stats = await admin_service.get_system_stats()
        
        users_stats = {
            "overview": stats.users,
            "activity_rate": (
                stats.users["active"] / stats.users["total"]
                if stats.users["total"] > 0 else 0
            ),
            "average_quota_per_user": (
                stats.quota["total_distributed"] / 3600 / stats.users["total"]
                if stats.users["total"] > 0 else 0
            ),
            "average_usage_per_user": (
                stats.quota["total_used"] / 3600 / stats.users["total"]
                if stats.users["total"] > 0 else 0
            )
        }
        
        logger.info(f"管理员 {admin_user} 查看用户统计")
        
        return AdminResponse(
            success=True,
            data=users_stats,
            message="用户统计获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "统计失败",
                "message": "获取用户统计时发生错误"
            }
        )

@router.get("/admin-info", response_model=AdminResponse, summary="管理员信息")
async def get_admin_info(
    admin_user: str = Depends(verify_admin_jwt)
):
    """
    获取管理员信息
    
    当前管理员和系统管理员列表
    """
    try:
        admin_users = await get_admin_users_list()
        
        admin_info = {
            "current_admin": admin_user,
            "admin_users": admin_users,
            "admin_count": len(admin_users),
            "system_info": {
                "admin_api_enabled": True,
                "quota_system_enabled": True
            }
        }
        
        logger.info(f"管理员 {admin_user} 查看管理员信息")
        
        return AdminResponse(
            success=True,
            data=admin_info,
            message="管理员信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取管理员信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "查询失败",
                "message": "获取管理员信息时发生错误"
            }
        )
