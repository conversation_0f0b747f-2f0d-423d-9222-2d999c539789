#!/usr/bin/env python3
"""
启动开发服务器脚本
用于本地开发和测试
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动服务器"""
    print("🚀 启动 ElevenLabs STT Backend 开发服务器...")
    print(f"📁 项目目录: {project_root}")
    
    # 确保必要的目录存在
    upload_dir = project_root / "uploads"
    logs_dir = project_root / "logs"
    upload_dir.mkdir(exist_ok=True)
    logs_dir.mkdir(exist_ok=True)
    
    print(f"📂 上传目录: {upload_dir}")
    print(f"📝 日志目录: {logs_dir}")
    print("🌐 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("\n按 Ctrl+C 停止服务器\n")
    
    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )

if __name__ == "__main__":
    main()
